<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KownBit</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: #fff;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header .subtitle {
            font-size: 1.2em;
            opacity: 0.8;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .card h3 {
            margin-bottom: 15px;
            color: #ffd700;
            font-size: 1.3em;
        }
        
        .price-display {
            font-size: 2.5em;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
        }
        
        .price-change {
            text-align: center;
            font-size: 1.2em;
            margin-bottom: 10px;
        }
        
        .positive { color: #00ff88; }
        .negative { color: #ff4757; }
        .neutral { color: #ffd700; }
        
        .prediction-box {
            background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .confidence-bar {
            width: 100%;
            height: 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .confidence-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff4757, #ffd700, #00ff88);
            transition: width 0.3s ease;
        }
        
        .indicator-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }
        
        .indicator-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 10px;
            border-radius: 8px;
            text-align: center;
        }
        
        .indicator-label {
            font-size: 0.9em;
            opacity: 0.8;
            margin-bottom: 5px;
        }
        
        .indicator-value {
            font-size: 1.1em;
            font-weight: bold;
        }
        
        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }
        
        .symbol-selector {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .symbol-selector select {
            background: rgba(255, 255, 255, 0.1);
            color: #fff;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            padding: 10px 15px;
            font-size: 1.1em;
        }
        
        .symbol-selector option {
            background: #2a5298;
            color: #fff;
        }
        
        .trading-advice {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            margin: 20px 0;
        }
        
        .advice-action {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .advice-reasoning {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-online { background: #00ff88; }
        .status-offline { background: #ff4757; }
        
        .refresh-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            margin: 10px 5px;
            transition: transform 0.2s;
        }
        
        .refresh-btn:hover {
            transform: translateY(-2px);
        }
        
        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .price-display {
                font-size: 2em;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 KownBit 币安期货预判系统</h1>
            <p class="subtitle">AI驱动的比特币价格预测 | 准确率目标: 80%+</p>
            <div class="symbol-selector">
                <select id="symbolSelect" onchange="changeSymbol()">
                    <option value="BTCUSDT" {{if eq .Symbol "BTCUSDT"}}selected{{end}}>BTC/USDT</option>
                    <option value="ETHUSDT" {{if eq .Symbol "ETHUSDT"}}selected{{end}}>ETH/USDT</option>
                    <option value="BNBUSDT" {{if eq .Symbol "BNBUSDT"}}selected{{end}}>BNB/USDT</option>
                </select>
                <button class="refresh-btn" onclick="refreshData()">🔄 刷新数据</button>
            </div>
        </div>

        <div class="dashboard-grid">
            <!-- 当前价格卡片 -->
            <div class="card">
                <h3>💰 当前价格</h3>
                <div class="price-display" id="currentPrice">
                    ${{printf "%.2f" .CurrentPrice}}
                </div>
                <div class="price-change" id="priceChange">
                    {{if .Stats}}
                        {{if .Stats.PriceChangePercent}}
                        {{$changePercent := parseFloat .Stats.PriceChangePercent}}
                        <span class="{{if gt $changePercent 0}}positive{{else if lt $changePercent 0}}negative{{else}}neutral{{end}}">
                            {{.Stats.PriceChangePercent}}% (24h)
                        </span>
                        {{else}}
                        <span class="neutral">0.00% (24h)</span>
                        {{end}}
                    {{else}}
                        <span class="neutral">0.00% (24h)</span>
                    {{end}}
                </div>
                <div style="text-align: center; font-size: 0.9em; opacity: 0.8;">
                    <span class="status-indicator status-online"></span>实时更新
                </div>
            </div>

            <!-- 预测结果卡片 -->
            <div class="card">
                <h3>🔮 10分钟预测</h3>
                {{if .Prediction}}
                <div class="prediction-box">
                    <div style="text-align: center;">
                        <div style="font-size: 1.8em; margin-bottom: 10px;">
                            ${{printf "%.2f" .Prediction.PredictedPrice}}
                        </div>
                        <div class="{{if eq .Prediction.Direction "UP"}}positive{{else if eq .Prediction.Direction "DOWN"}}negative{{else}}neutral{{end}}">
                            {{.Prediction.Direction}} {{printf "%.2f" .Prediction.PercentChange}}%
                        </div>
                    </div>
                    <div class="confidence-bar">
                        <div class="confidence-fill" style="width: {{printf "%.0f" (mul .Prediction.Confidence 100)}}%"></div>
                    </div>
                    <div style="text-align: center; font-size: 0.9em;">
                        置信度: {{printf "%.1f" (mul .Prediction.Confidence 100)}}%
                    </div>
                </div>
                {{else}}
                <div style="text-align: center; opacity: 0.7;">
                    正在生成预测...
                </div>
                {{end}}
            </div>

            <!-- 技术指标卡片 -->
            <div class="card">
                <h3>📊 技术分析</h3>
                {{if .Technical}}
                <div class="indicator-grid">
                    <div class="indicator-item">
                        <div class="indicator-label">RSI</div>
                        <div class="indicator-value {{if lt .Technical.RSI 30}}positive{{else if gt .Technical.RSI 70}}negative{{else}}neutral{{end}}">
                            {{printf "%.1f" .Technical.RSI}}
                        </div>
                    </div>
                    <div class="indicator-item">
                        <div class="indicator-label">MACD</div>
                        <div class="indicator-value {{if gt .Technical.MACD 0}}positive{{else}}negative{{end}}">
                            {{printf "%.4f" .Technical.MACD}}
                        </div>
                    </div>
                    <div class="indicator-item">
                        <div class="indicator-label">MA20</div>
                        <div class="indicator-value">
                            ${{printf "%.2f" .Technical.MA20}}
                        </div>
                    </div>
                    <div class="indicator-item">
                        <div class="indicator-label">信号</div>
                        <div class="indicator-value {{if eq .Technical.Signal "BUY"}}positive{{else if eq .Technical.Signal "SELL"}}negative{{else}}neutral{{end}}">
                            {{.Technical.Signal}}
                        </div>
                    </div>
                </div>
                {{else}}
                <div style="text-align: center; opacity: 0.7;">
                    正在计算技术指标...
                </div>
                {{end}}
            </div>

            <!-- 情绪分析卡片 -->
            <div class="card">
                <h3>😊 市场情绪</h3>
                {{if .Sentiment}}
                <div style="text-align: center;">
                    <div style="font-size: 2em; margin: 15px 0;">
                        {{if gt .Sentiment.OverallSentiment 0.2}}😄{{else if lt .Sentiment.OverallSentiment -0.2}}😰{{else}}😐{{end}}
                    </div>
                    <div class="{{if gt .Sentiment.OverallSentiment 0}}positive{{else if lt .Sentiment.OverallSentiment 0}}negative{{else}}neutral{{end}}">
                        {{printf "%.2f" .Sentiment.OverallSentiment}} ({{.Sentiment.Trend}})
                    </div>
                    <div style="font-size: 0.9em; margin-top: 10px; opacity: 0.8;">
                        新闻: {{.Sentiment.NewsCount}} | 社交: {{.Sentiment.SocialCount}}
                    </div>
                </div>
                {{else}}
                <div style="text-align: center; opacity: 0.7;">
                    正在分析市场情绪...
                </div>
                {{end}}
            </div>
        </div>

        <!-- 交易建议 -->
        <div class="trading-advice" id="tradingAdvice">
            <div class="advice-action">💡 正在生成交易建议...</div>
            <div class="advice-reasoning">请稍候，系统正在综合分析各项指标</div>
        </div>

        <!-- 价格图表 -->
        <div class="card">
            <h3>📈 价格走势图</h3>
            <div class="chart-container">
                <canvas id="priceChart"></canvas>
            </div>
        </div>

        <!-- 准确率统计 -->
        <div class="card">
            <h3>🎯 预测准确率</h3>
            <div class="indicator-grid">
                <div class="indicator-item">
                    <div class="indicator-label">总预测次数</div>
                    <div class="indicator-value" id="totalPredictions">-</div>
                </div>
                <div class="indicator-item">
                    <div class="indicator-label">准确次数</div>
                    <div class="indicator-value" id="accuratePredictions">-</div>
                </div>
                <div class="indicator-item">
                    <div class="indicator-label">准确率</div>
                    <div class="indicator-value" id="accuracyRate">-</div>
                </div>
                <div class="indicator-item">
                    <div class="indicator-label">平均误差</div>
                    <div class="indicator-value" id="avgError">-</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentSymbol = '{{.Symbol}}';
        let priceChart;
        let ws;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initChart();
            connectWebSocket();
            loadTradingAdvice();
            loadAccuracyStats();
            
            // 每30秒刷新一次数据
            setInterval(refreshData, 30000);
        });

        // 初始化图表
        function initChart() {
            const ctx = document.getElementById('priceChart').getContext('2d');
            priceChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '价格',
                        data: [],
                        borderColor: '#00ff88',
                        backgroundColor: 'rgba(0, 255, 136, 0.1)',
                        tension: 0.4
                    }, {
                        label: '预测价格',
                        data: [],
                        borderColor: '#ffd700',
                        backgroundColor: 'rgba(255, 215, 0, 0.1)',
                        borderDash: [5, 5],
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: {
                                color: '#fff'
                            }
                        }
                    },
                    scales: {
                        x: {
                            ticks: { color: '#fff' },
                            grid: { color: 'rgba(255, 255, 255, 0.1)' }
                        },
                        y: {
                            ticks: { color: '#fff' },
                            grid: { color: 'rgba(255, 255, 255, 0.1)' }
                        }
                    }
                }
            });
            
            loadHistoricalData();
        }

        // 连接WebSocket
        function connectWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            ws = new WebSocket(`${protocol}//${window.location.host}/ws?symbol=${currentSymbol}`);
            
            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                if (data.type === 'price_update') {
                    updatePrice(data.currentPrice);
                    if (data.prediction) {
                        updatePrediction(data.prediction);
                    }
                }
            };
            
            ws.onclose = function() {
                // 重连
                setTimeout(connectWebSocket, 5000);
            };
        }

        // 更新价格显示
        function updatePrice(price) {
            document.getElementById('currentPrice').textContent = `$${price.toFixed(2)}`;
        }

        // 更新预测显示
        function updatePrediction(prediction) {
            // 这里可以更新预测相关的DOM元素
        }

        // 加载历史数据
        function loadHistoricalData() {
            axios.get(`/api/historical?symbol=${currentSymbol}&interval=1m&limit=60`)
                .then(response => {
                    const data = response.data;
                    const labels = data.map(item => new Date(item.OpenTime).toLocaleTimeString());
                    const prices = data.map(item => item.Close);
                    
                    priceChart.data.labels = labels;
                    priceChart.data.datasets[0].data = prices;
                    priceChart.update();
                })
                .catch(error => console.error('Error loading historical data:', error));
        }

        // 加载交易建议
        function loadTradingAdvice() {
            axios.get(`/api/trading-advice?symbol=${currentSymbol}`)
                .then(response => {
                    const advice = response.data;
                    const adviceElement = document.getElementById('tradingAdvice');
                    
                    let actionClass = 'neutral';
                    if (advice.action === 'BUY' || advice.action === 'STRONG_BUY') {
                        actionClass = 'positive';
                    } else if (advice.action === 'SELL' || advice.action === 'STRONG_SELL') {
                        actionClass = 'negative';
                    }
                    
                    adviceElement.innerHTML = `
                        <div class="advice-action ${actionClass}">${getActionEmoji(advice.action)} ${advice.action}</div>
                        <div class="advice-reasoning">${advice.reasoning}</div>
                        <div style="margin-top: 10px; font-size: 0.9em;">
                            置信度: ${(advice.confidence * 100).toFixed(1)}% | 风险等级: ${advice.risk_level}
                        </div>
                    `;
                })
                .catch(error => console.error('Error loading trading advice:', error));
        }

        // 加载准确率统计
        function loadAccuracyStats() {
            axios.get(`/api/accuracy-stats?symbol=${currentSymbol}`)
                .then(response => {
                    const stats = response.data;
                    document.getElementById('totalPredictions').textContent = stats.total_predictions;
                    document.getElementById('accuratePredictions').textContent = stats.accurate_predictions;
                    document.getElementById('accuracyRate').textContent = `${(stats.accuracy_rate * 100).toFixed(1)}%`;
                    document.getElementById('avgError').textContent = `${(stats.avg_error * 100).toFixed(2)}%`;
                })
                .catch(error => console.error('Error loading accuracy stats:', error));
        }

        // 获取动作表情符号
        function getActionEmoji(action) {
            const emojis = {
                'BUY': '🟢',
                'STRONG_BUY': '🚀',
                'SELL': '🔴',
                'STRONG_SELL': '⬇️',
                'HOLD': '⏸️'
            };
            return emojis[action] || '❓';
        }

        // 切换交易对
        function changeSymbol() {
            const select = document.getElementById('symbolSelect');
            currentSymbol = select.value;
            window.location.href = `/?symbol=${currentSymbol}`;
        }

        // 刷新数据
        function refreshData() {
            location.reload();
        }
    </script>
</body>
</html>
