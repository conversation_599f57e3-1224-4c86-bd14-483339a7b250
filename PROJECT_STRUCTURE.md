# KownBit 项目结构说明

## 📁 目录结构

```
kownbit/
├── 📄 main.go                    # 主程序入口
├── 📄 go.mod                     # Go模块依赖
├── 📄 go.sum                     # 依赖版本锁定
├── 📄 README.md                  # 项目说明文档
├── 📄 USAGE.md                   # 使用指南
├── 📄 PROJECT_STRUCTURE.md       # 项目结构说明
├── 📄 .env.example               # 环境变量示例
├── 📄 Dockerfile                 # Docker构建文件
├── 📄 docker-compose.yml         # Docker编排文件
├── 📄 demo.bat                   # Windows演示脚本
│
├── 📁 config/                    # 配置模块
│   └── 📄 config.go              # 配置管理和数据模型
│
├── 📁 services/                  # 业务服务层
│   ├── 📄 binance_service.go     # 币安API服务
│   ├── 📄 analysis_service.go    # 技术分析服务
│   ├── 📄 prediction_service.go  # 预测算法服务
│   └── 📄 sentiment_service.go   # 情绪分析服务
│
├── 📁 handlers/                  # HTTP处理器
│   └── 📄 handlers.go            # Web API处理器
│
├── 📁 templates/                 # HTML模板
│   └── 📄 dashboard.html         # 主页面模板
│
├── 📁 static/                    # 静态资源
│   ├── 📁 css/                   # 样式文件
│   │   └── 📄 style.css          # 主样式文件
│   └── 📁 js/                    # JavaScript文件
│       └── 📄 app.js             # 主应用脚本
│
└── 📁 scripts/                   # 部署脚本
    ├── 📄 setup.sh               # 安装脚本
    └── 📄 run.sh                 # 运行脚本
```

## 🏗️ 架构设计

### 分层架构
```
┌─────────────────────────────────────────┐
│              展示层 (Presentation)        │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │  Web UI     │  │   REST API      │   │
│  │ (HTML/CSS/JS)│  │  (JSON/HTTP)    │   │
│  └─────────────┘  └─────────────────┘   │
└─────────────────────────────────────────┘
                    │
┌─────────────────────────────────────────┐
│              业务层 (Business)           │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │ Handlers    │  │   Services      │   │
│  │ (路由处理)   │  │  (业务逻辑)      │   │
│  └─────────────┘  └─────────────────┘   │
└─────────────────────────────────────────┘
                    │
┌─────────────────────────────────────────┐
│              数据层 (Data)               │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │ PostgreSQL  │  │     Redis       │   │
│  │ (持久化存储) │  │   (缓存存储)     │   │
│  └─────────────┘  └─────────────────┘   │
└─────────────────────────────────────────┘
```

## 📋 核心模块详解

### 1. 主程序 (main.go)
- **功能**: 应用程序启动入口
- **职责**: 
  - 初始化配置
  - 启动各种服务
  - 设置HTTP路由
  - 启动Web服务器

### 2. 配置模块 (config/)
- **config.go**: 
  - 环境变量管理
  - 数据库连接配置
  - Redis连接配置
  - 数据模型定义

### 3. 服务层 (services/)

#### BinanceService (binance_service.go)
- **功能**: 币安API集成
- **主要方法**:
  - `StartDataCollection()`: 启动数据采集
  - `GetCurrentPrice()`: 获取当前价格
  - `GetHistoricalKlines()`: 获取历史K线数据
  - `GetOrderBook()`: 获取深度数据

#### AnalysisService (analysis_service.go)
- **功能**: 技术分析计算
- **主要方法**:
  - `CalculateTechnicalIndicators()`: 计算技术指标
  - `calculateRSI()`: 计算RSI指标
  - `calculateMACD()`: 计算MACD指标
  - `calculateBollingerBands()`: 计算布林带
  - `generateSignal()`: 生成交易信号

#### PredictionService (prediction_service.go)
- **功能**: 价格预测算法
- **主要方法**:
  - `StartPredictionEngine()`: 启动预测引擎
  - `generatePrediction()`: 生成预测结果
  - `linearRegressionPrediction()`: 线性回归预测
  - `movingAveragePrediction()`: 移动平均预测
  - `momentumPrediction()`: 动量预测

#### SentimentService (sentiment_service.go)
- **功能**: 情绪分析
- **主要方法**:
  - `StartSentimentCollection()`: 启动情绪数据收集
  - `collectNewsData()`: 收集新闻数据
  - `analyzeSentiment()`: 分析文本情绪
  - `GetSentimentAnalysis()`: 获取情绪分析结果

### 4. 处理器层 (handlers/)

#### Handler (handlers.go)
- **功能**: HTTP请求处理
- **主要路由**:
  - `GET /`: 主页面
  - `GET /api/prediction`: 获取预测结果
  - `GET /api/analysis`: 获取技术分析
  - `GET /api/sentiment`: 获取情绪分析
  - `GET /ws`: WebSocket连接

### 5. 前端资源

#### HTML模板 (templates/)
- **dashboard.html**: 主页面模板
  - 响应式设计
  - 实时数据展示
  - 图表可视化
  - WebSocket集成

#### CSS样式 (static/css/)
- **style.css**: 主样式文件
  - 现代化UI设计
  - 渐变背景
  - 动画效果
  - 响应式布局

#### JavaScript (static/js/)
- **app.js**: 主应用脚本
  - WebSocket通信
  - 图表渲染
  - 实时数据更新
  - 用户交互

## 🔄 数据流程

### 1. 数据采集流程
```
币安API → BinanceService → PostgreSQL
   ↓
新闻网站 → SentimentService → PostgreSQL
   ↓
社交媒体 → SentimentService → PostgreSQL
```

### 2. 预测流程
```
历史数据 → AnalysisService → 技术指标
    ↓
技术指标 → PredictionService → 预测算法
    ↓
情绪数据 → PredictionService → 综合预测
    ↓
预测结果 → Redis缓存 → 前端展示
```

### 3. 前端更新流程
```
用户访问 → Handler → 模板渲染 → HTML页面
    ↓
WebSocket → 实时数据 → JavaScript更新 → DOM更新
```

## 🗄️ 数据模型

### KlineData (K线数据)
```go
type KlineData struct {
    ID          uint
    Symbol      string    // 交易对
    OpenTime    int64     // 开盘时间
    CloseTime   int64     // 收盘时间
    Open        float64   // 开盘价
    High        float64   // 最高价
    Low         float64   // 最低价
    Close       float64   // 收盘价
    Volume      float64   // 成交量
    QuoteVolume float64   // 成交额
    Interval    string    // 时间间隔
}
```

### PredictionResult (预测结果)
```go
type PredictionResult struct {
    ID              uint
    Symbol          string    // 交易对
    PredictedPrice  float64   // 预测价格
    CurrentPrice    float64   // 当前价格
    Confidence      float64   // 置信度
    Direction       string    // 方向
    PredictionTime  int64     // 预测时间
    TargetTime      int64     // 目标时间
    ActualPrice     *float64  // 实际价格
    IsAccurate      *bool     // 是否准确
}
```

### SentimentData (情绪数据)
```go
type SentimentData struct {
    ID        uint
    Source    string    // 数据源
    Content   string    // 内容
    Sentiment float64   // 情绪得分
    Timestamp int64     // 时间戳
    Symbol    string    // 交易对
}
```

### TechnicalIndicator (技术指标)
```go
type TechnicalIndicator struct {
    ID             uint
    Symbol         string    // 交易对
    Timestamp      int64     // 时间戳
    RSI            float64   // RSI指标
    MACD           float64   // MACD指标
    MACDSignal     float64   // MACD信号线
    MA20           float64   // 20日均线
    MA50           float64   // 50日均线
    EMA12          float64   // 12日指数均线
    EMA26          float64   // 26日指数均线
    BollingerUpper float64   // 布林带上轨
    BollingerLower float64   // 布林带下轨
    Volume         float64   // 成交量
}
```

## 🚀 部署方式

### 1. 本地开发
```bash
go run main.go
```

### 2. 编译部署
```bash
go build -o kownbit .
./kownbit
```

### 3. Docker部署
```bash
docker-compose up -d
```

### 4. 生产环境
```bash
# 使用systemd管理
sudo systemctl start kownbit
sudo systemctl enable kownbit
```

## 🔧 扩展指南

### 添加新的技术指标
1. 在`AnalysisService`中添加计算方法
2. 更新`TechnicalIndicator`模型
3. 在前端添加显示组件

### 添加新的预测算法
1. 在`PredictionService`中实现算法
2. 更新权重配置
3. 测试算法准确率

### 添加新的数据源
1. 创建新的Service
2. 实现数据采集逻辑
3. 更新数据模型

---

这个项目结构设计遵循了清晰的分层架构原则，便于维护和扩展。每个模块都有明确的职责，代码组织清晰，易于理解和修改。
