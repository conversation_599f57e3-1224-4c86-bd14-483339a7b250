@echo off
echo ========================================
echo 🚀 KownBit 快速启动 - 优化版本
echo ========================================
echo.

echo 📝 检查配置...
if not exist .env (
    echo DATABASE_URL=sqlite://kownbit.db > .env
    echo REDIS_URL=redis://localhost:6379 >> .env
    echo BINANCE_API_KEY=demo_api_key >> .env
    echo BINANCE_SECRET_KEY=demo_secret_key >> .env
    echo NEWS_API_KEY=demo_news_key >> .env
    echo PORT=8080 >> .env
    echo LOG_LEVEL=info >> .env
    echo ENVIRONMENT=demo >> .env
    echo DEMO_MODE=true >> .env
    echo ✅ 配置文件已创建
)

echo.
echo 🔧 编译程序...
go build -o kownbit.exe .
if %errorlevel% neq 0 (
    echo ❌ 编译失败
    pause
    exit /b 1
)

echo ✅ 编译成功
echo.
echo 🎯 启动服务器...
echo 📱 服务器启动后会自动打开浏览器
echo 🛑 按 Ctrl+C 停止服务器
echo.

timeout /t 2 /nobreak >nul
start "" "http://localhost:8080"
kownbit.exe
