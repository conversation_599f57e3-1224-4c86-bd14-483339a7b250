#!/bin/bash

echo "🚀 启动 KownBit 币安期货预判系统"
echo "================================"

# 检查.env文件
if [ ! -f .env ]; then
    echo "❌ .env文件不存在，请先运行 ./scripts/setup.sh"
    exit 1
fi

# 检查数据库连接
echo "🔍 检查数据库连接..."
if ! pg_isready -h localhost -p 5432 &> /dev/null; then
    echo "⚠️  PostgreSQL未运行，请启动PostgreSQL服务"
fi

# 检查Redis连接
echo "🔍 检查Redis连接..."
if ! redis-cli ping &> /dev/null; then
    echo "⚠️  Redis未运行，请启动Redis服务"
fi

# 启动应用
echo "🎯 启动应用..."
go run main.go
