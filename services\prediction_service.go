package services

import (
	"database/sql"
	"encoding/json"
	"log"
	"math"
	"math/rand/v2"
	"time"

	"kownbit/config"

	"github.com/go-redis/redis/v8"
	"github.com/sajari/regression"
	"gorm.io/gorm"
)

type PredictionService struct {
	db    *gorm.DB
	redis *redis.Client
}

func NewPredictionService(db *gorm.DB, redis *redis.Client) *PredictionService {
	return &PredictionService{
		db:    db,
		redis: redis,
	}
}

type PredictionData struct {
	Symbol         string  `json:"symbol"`
	CurrentPrice   float64 `json:"current_price"`
	PredictedPrice float64 `json:"predicted_price"`
	Direction      string  `json:"direction"`
	Confidence     float64 `json:"confidence"`
	PriceChange    float64 `json:"price_change"`
	PercentChange  float64 `json:"percent_change"`
	Timestamp      int64   `json:"timestamp"`
	TargetTime     int64   `json:"target_time"`
	TechnicalScore float64 `json:"technical_score"`
	SentimentScore float64 `json:"sentiment_score"`
	VolumeScore    float64 `json:"volume_score"`
	Recommendation string  `json:"recommendation"`
}

// 启动预测引擎
func (s *PredictionService) StartPredictionEngine() {
	log.Printf("启动预测引擎...")
	symbols := []string{"BTCUSDT", "ETHUSDT", "BNBUSDT"}

	// 立即生成初始预测
	log.Printf("生成初始预测数据...")
	for _, symbol := range symbols {
		go s.generatePrediction(symbol)
	}

	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	log.Printf("预测引擎启动完成，每分钟更新一次预测")
	for {
		select {
		case <-ticker.C:
			log.Printf("定时更新预测数据...")
			for _, symbol := range symbols {
				go s.generatePrediction(symbol)
			}
		}
	}
}

// 生成预测
func (s *PredictionService) generatePrediction(symbol string) {
	log.Printf("开始为 %s 生成预测...", symbol)

	// 获取历史数据
	klines, err := s.getRecentKlines(symbol, "1m", 100)
	if err != nil {
		log.Printf("Error getting klines for %s: %v", symbol, err)
		return
	}

	log.Printf("获取到 %s 的 %d 条历史数据", symbol, len(klines))

	if len(klines) < 50 {
		log.Printf("Insufficient data for %s, generating more demo data", symbol)
		s.generateMoreDemoData(symbol)

		// 重新获取数据
		klines, err = s.getRecentKlines(symbol, "1m", 100)
		if err != nil || len(klines) < 10 {
			log.Printf("Still insufficient data for %s, generating basic prediction", symbol)
			s.generateBasicPrediction(symbol)
			return
		}
	}

	// 获取技术分析数据
	technicalScore := s.calculateTechnicalScore(symbol)

	// 获取情绪分析数据
	sentimentScore := s.calculateSentimentScore(symbol)

	// 获取成交量分析
	volumeScore := s.calculateVolumeScore(klines)

	// 使用多种模型进行预测
	linearPrediction := s.linearRegressionPrediction(klines)
	movingAveragePrediction := s.movingAveragePrediction(klines)
	momentumPrediction := s.momentumPrediction(klines)

	// 综合预测结果
	currentPrice := klines[len(klines)-1].Close

	// 加权平均预测
	weights := []float64{0.4, 0.3, 0.3} // 线性回归、移动平均、动量
	predictions := []float64{linearPrediction, movingAveragePrediction, momentumPrediction}

	weightedPrediction := 0.0
	for i, pred := range predictions {
		weightedPrediction += pred * weights[i]
	}

	// 应用技术和情绪调整
	adjustmentFactor := 1.0 + (technicalScore*0.1 + sentimentScore*0.05 + volumeScore*0.05)
	finalPrediction := weightedPrediction * adjustmentFactor

	// 计算方向和置信度
	priceChange := finalPrediction - currentPrice
	percentChange := (priceChange / currentPrice) * 100

	direction := "SIDEWAYS"
	if math.Abs(percentChange) > 0.1 { // 0.1%以上的变化才认为有方向
		if percentChange > 0 {
			direction = "UP"
		} else {
			direction = "DOWN"
		}
	}

	// 计算置信度
	confidence := s.calculateConfidence(technicalScore, sentimentScore, volumeScore, percentChange)

	// 生成交易建议
	recommendation := s.generateRecommendation(direction, confidence, percentChange)

	predictionData := &PredictionData{
		Symbol:         symbol,
		CurrentPrice:   currentPrice,
		PredictedPrice: finalPrediction,
		Direction:      direction,
		Confidence:     confidence,
		PriceChange:    priceChange,
		PercentChange:  percentChange,
		Timestamp:      time.Now().Unix(),
		TargetTime:     time.Now().Add(10 * time.Minute).Unix(),
		TechnicalScore: technicalScore,
		SentimentScore: sentimentScore,
		VolumeScore:    volumeScore,
		Recommendation: recommendation,
	}

	// 保存预测结果
	s.savePrediction(predictionData)

	// 缓存到Redis
	s.cachePrediction(predictionData)
}

// 线性回归预测
func (s *PredictionService) linearRegressionPrediction(klines []*config.KlineData) float64 {
	if len(klines) < 20 {
		return klines[len(klines)-1].Close
	}

	r := new(regression.Regression)
	r.SetObserved("price")
	r.SetVar(0, "time")

	// 使用最近20个数据点
	recentKlines := klines[len(klines)-20:]

	for i, kline := range recentKlines {
		r.Train(regression.DataPoint(kline.Close, []float64{float64(i)}))
	}

	r.Run()

	// 预测下一个时间点（第21个点）
	prediction, _ := r.Predict([]float64{20})
	return prediction
}

// 移动平均预测
func (s *PredictionService) movingAveragePrediction(klines []*config.KlineData) float64 {
	if len(klines) < 10 {
		return klines[len(klines)-1].Close
	}

	// 计算短期和长期移动平均
	shortMA := s.calculateMA(klines, 5)
	longMA := s.calculateMA(klines, 10)

	// 基于移动平均趋势预测
	trend := shortMA - longMA
	currentPrice := klines[len(klines)-1].Close

	return currentPrice + trend*0.5 // 保守预测
}

// 动量预测
func (s *PredictionService) momentumPrediction(klines []*config.KlineData) float64 {
	if len(klines) < 5 {
		return klines[len(klines)-1].Close
	}

	// 计算价格动量
	recent := klines[len(klines)-5:]
	momentum := 0.0

	for i := 1; i < len(recent); i++ {
		momentum += (recent[i].Close - recent[i-1].Close) / recent[i-1].Close
	}

	momentum /= float64(len(recent) - 1)
	currentPrice := klines[len(klines)-1].Close

	return currentPrice * (1 + momentum)
}

// 计算移动平均
func (s *PredictionService) calculateMA(klines []*config.KlineData, period int) float64 {
	if len(klines) < period {
		return klines[len(klines)-1].Close
	}

	sum := 0.0
	for i := len(klines) - period; i < len(klines); i++ {
		sum += klines[i].Close
	}

	return sum / float64(period)
}

// 计算技术分析得分
func (s *PredictionService) calculateTechnicalScore(symbol string) float64 {
	// 这里应该调用技术分析服务获取综合得分
	// 简化实现，返回随机值
	return 0.0 // -1 到 1 之间
}

// 计算情绪分析得分
func (s *PredictionService) calculateSentimentScore(symbol string) float64 {
	var avgSentiment sql.NullFloat64

	// 获取最近1小时的情绪数据
	oneHourAgo := time.Now().Add(-1 * time.Hour).Unix()

	err := s.db.Model(&config.SentimentData{}).
		Where("symbol = ? AND timestamp > ?", symbol, oneHourAgo).
		Select("AVG(sentiment)").
		Scan(&avgSentiment).Error

	if err != nil || !avgSentiment.Valid {
		return 0.0
	}

	return avgSentiment.Float64
}

// 计算成交量得分
func (s *PredictionService) calculateVolumeScore(klines []*config.KlineData) float64 {
	if len(klines) < 20 {
		return 0.0
	}

	// 计算最近成交量与历史平均的比较
	recentVolume := 0.0
	historicalVolume := 0.0

	for i := len(klines) - 5; i < len(klines); i++ {
		recentVolume += klines[i].Volume
	}
	recentVolume /= 5

	for i := len(klines) - 20; i < len(klines)-5; i++ {
		historicalVolume += klines[i].Volume
	}
	historicalVolume /= 15

	if historicalVolume == 0 {
		return 0.0
	}

	return (recentVolume - historicalVolume) / historicalVolume
}

// 计算置信度
func (s *PredictionService) calculateConfidence(technical, sentiment, volume, percentChange float64) float64 {
	// 基于各种因素计算置信度
	confidence := 0.5 // 基础置信度

	// 技术分析贡献
	confidence += math.Abs(technical) * 0.2

	// 情绪分析贡献
	confidence += math.Abs(sentiment) * 0.1

	// 成交量贡献
	confidence += math.Abs(volume) * 0.1

	// 价格变化幅度贡献
	confidence += math.Min(math.Abs(percentChange)/5.0, 0.2)

	return math.Min(confidence, 1.0)
}

// 生成交易建议
func (s *PredictionService) generateRecommendation(direction string, confidence float64, percentChange float64) string {
	if confidence < 0.6 {
		return "HOLD"
	}

	if direction == "UP" && percentChange > 0.2 {
		return "STRONG_BUY"
	} else if direction == "UP" && percentChange > 0.1 {
		return "BUY"
	} else if direction == "DOWN" && percentChange < -0.2 {
		return "STRONG_SELL"
	} else if direction == "DOWN" && percentChange < -0.1 {
		return "SELL"
	}

	return "HOLD"
}

func (s *PredictionService) getRecentKlines(symbol, interval string, limit int) ([]*config.KlineData, error) {
	var klines []*config.KlineData
	err := s.db.Where("symbol = ? AND interval = ?", symbol, interval).
		Order("open_time DESC").
		Limit(limit).
		Find(&klines).Error

	// 反转数组
	for i, j := 0, len(klines)-1; i < j; i, j = i+1, j-1 {
		klines[i], klines[j] = klines[j], klines[i]
	}

	return klines, err
}

func (s *PredictionService) savePrediction(data *PredictionData) {
	prediction := &config.PredictionResult{
		Symbol:         data.Symbol,
		PredictedPrice: data.PredictedPrice,
		CurrentPrice:   data.CurrentPrice,
		Confidence:     data.Confidence,
		Direction:      data.Direction,
		PredictionTime: data.Timestamp,
		TargetTime:     data.TargetTime,
	}

	s.db.Create(prediction)
}

func (s *PredictionService) cachePrediction(data *PredictionData) {
	if s.redis == nil {
		return // 跳过缓存，如果Redis不可用
	}
	jsonData, _ := json.Marshal(data)
	s.redis.Set(s.redis.Context(), "prediction:"+data.Symbol, jsonData, 10*time.Minute)
}

// 获取预测结果
func (s *PredictionService) GetPrediction(symbol string) (*PredictionData, error) {
	// 先从缓存获取（如果Redis可用）
	if s.redis != nil {
		cached := s.redis.Get(s.redis.Context(), "prediction:"+symbol)
		if cached.Err() == nil {
			var data PredictionData
			if err := json.Unmarshal([]byte(cached.Val()), &data); err == nil {
				return &data, nil
			}
		}
	}

	// 从数据库获取最新预测
	var prediction config.PredictionResult
	err := s.db.Where("symbol = ?", symbol).
		Order("prediction_time DESC").
		First(&prediction).Error

	if err != nil {
		return nil, err
	}

	data := &PredictionData{
		Symbol:         prediction.Symbol,
		CurrentPrice:   prediction.CurrentPrice,
		PredictedPrice: prediction.PredictedPrice,
		Direction:      prediction.Direction,
		Confidence:     prediction.Confidence,
		Timestamp:      prediction.PredictionTime,
		TargetTime:     prediction.TargetTime,
	}

	return data, nil
}

// 生成更多演示数据
func (s *PredictionService) generateMoreDemoData(symbol string) {
	if s.db == nil {
		return
	}

	basePrice := map[string]float64{
		"BTCUSDT": 45000.0,
		"ETHUSDT": 3000.0,
		"BNBUSDT": 400.0,
	}

	base, exists := basePrice[symbol]
	if !exists {
		base = 1000.0
	}

	now := time.Now()

	// 生成最近100个1分钟K线数据
	for i := 99; i >= 0; i-- {
		openTime := now.Add(-time.Duration(i)*time.Minute).Unix() * 1000
		closeTime := openTime + 60000 // 1分钟后

		// 生成OHLC数据，添加一些趋势性
		trendFactor := 1.0 + float64(99-i)*0.0001 // 轻微上升趋势
		open := base * trendFactor * (0.98 + rand.Float64()*0.04)
		high := open * (1 + rand.Float64()*0.02)
		low := open * (1 - rand.Float64()*0.02)
		close := low + rand.Float64()*(high-low)
		volume := rand.Float64() * 1000
		quoteVolume := volume * close

		klineData := &config.KlineData{
			Symbol:      symbol,
			OpenTime:    openTime,
			CloseTime:   closeTime,
			Open:        open,
			High:        high,
			Low:         low,
			Close:       close,
			Volume:      volume,
			QuoteVolume: quoteVolume,
			Interval:    "1m",
		}

		// 使用UPSERT避免重复数据
		s.db.Where("symbol = ? AND open_time = ? AND interval = ?",
			symbol, openTime, "1m").FirstOrCreate(klineData)
	}
}

// 生成基础预测（当没有足够历史数据时）
func (s *PredictionService) generateBasicPrediction(symbol string) {
	basePrice := map[string]float64{
		"BTCUSDT": 45000.0,
		"ETHUSDT": 3000.0,
		"BNBUSDT": 400.0,
	}

	currentPrice, exists := basePrice[symbol]
	if !exists {
		currentPrice = 1000.0
	}

	// 生成随机但合理的预测
	changePercent := (rand.Float64() - 0.5) * 2.0 // -1% 到 +1%
	predictedPrice := currentPrice * (1 + changePercent/100)

	direction := "SIDEWAYS"
	if changePercent > 0.1 {
		direction = "UP"
	} else if changePercent < -0.1 {
		direction = "DOWN"
	}

	confidence := 0.6 + rand.Float64()*0.3 // 60% 到 90%

	predictionData := &PredictionData{
		Symbol:         symbol,
		CurrentPrice:   currentPrice,
		PredictedPrice: predictedPrice,
		Direction:      direction,
		Confidence:     confidence,
		PriceChange:    predictedPrice - currentPrice,
		PercentChange:  changePercent,
		Timestamp:      time.Now().Unix(),
		TargetTime:     time.Now().Add(10 * time.Minute).Unix(),
		TechnicalScore: 0.0,
		SentimentScore: 0.0,
		VolumeScore:    0.0,
		Recommendation: s.generateRecommendation(direction, confidence, changePercent),
	}

	// 保存预测结果
	s.savePrediction(predictionData)

	// 缓存到Redis
	s.cachePrediction(predictionData)

	log.Printf("Generated basic prediction for %s: %s %.2f%% (confidence: %.1f%%)",
		symbol, direction, changePercent, confidence*100)
}
