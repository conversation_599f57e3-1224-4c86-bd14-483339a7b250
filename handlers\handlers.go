package handlers

import (
	"net/http"
	"strconv"
	"time"

	"kownbit/services"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

type Handler struct {
	binanceService    *services.BinanceService
	predictionService *services.PredictionService
	analysisService   *services.AnalysisService
	sentimentService  *services.SentimentService
}

func NewHandler(
	binanceService *services.BinanceService,
	predictionService *services.PredictionService,
	analysisService *services.AnalysisService,
	sentimentService *services.SentimentService,
) *Handler {
	return &Handler{
		binanceService:    binanceService,
		predictionService: predictionService,
		analysisService:   analysisService,
		sentimentService:  sentimentService,
	}
}

// 主页面
func (h *Handler) Dashboard(c *gin.Context) {
	symbol := c.DefaultQuery("symbol", "BTCUSDT")
	
	// 获取当前价格
	currentPrice, _ := h.binanceService.GetCurrentPrice(symbol)
	
	// 获取预测数据
	prediction, _ := h.predictionService.GetPrediction(symbol)
	
	// 获取技术分析
	technical, _ := h.analysisService.CalculateTechnicalIndicators(symbol, "1m")
	
	// 获取情绪分析
	sentiment, _ := h.sentimentService.GetSentimentAnalysis(symbol, 24)
	
	// 获取24小时统计
	stats, _ := h.binanceService.Get24hrStats(symbol)
	
	data := gin.H{
		"Symbol":      symbol,
		"CurrentPrice": currentPrice,
		"Prediction":  prediction,
		"Technical":   technical,
		"Sentiment":   sentiment,
		"Stats":       stats,
		"Timestamp":   time.Now().Unix(),
	}
	
	c.HTML(http.StatusOK, "dashboard.html", data)
}

// 获取预测API
func (h *Handler) GetPrediction(c *gin.Context) {
	symbol := c.DefaultQuery("symbol", "BTCUSDT")
	
	prediction, err := h.predictionService.GetPrediction(symbol)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusOK, prediction)
}

// 获取技术分析API
func (h *Handler) GetAnalysis(c *gin.Context) {
	symbol := c.DefaultQuery("symbol", "BTCUSDT")
	interval := c.DefaultQuery("interval", "1m")
	
	analysis, err := h.analysisService.CalculateTechnicalIndicators(symbol, interval)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusOK, analysis)
}

// 获取情绪分析API
func (h *Handler) GetSentiment(c *gin.Context) {
	symbol := c.DefaultQuery("symbol", "BTCUSDT")
	hoursStr := c.DefaultQuery("hours", "24")
	
	hours, err := strconv.Atoi(hoursStr)
	if err != nil {
		hours = 24
	}
	
	sentiment, err := h.sentimentService.GetSentimentAnalysis(symbol, hours)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusOK, sentiment)
}

// WebSocket升级器
var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		return true // 允许所有来源
	},
}

// WebSocket处理器
func (h *Handler) WebSocketHandler(c *gin.Context) {
	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		return
	}
	defer conn.Close()
	
	symbol := c.DefaultQuery("symbol", "BTCUSDT")
	
	// 创建ticker，每秒发送一次数据
	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			// 获取实时数据
			currentPrice, _ := h.binanceService.GetCurrentPrice(symbol)
			prediction, _ := h.predictionService.GetPrediction(symbol)
			
			data := map[string]interface{}{
				"type":         "price_update",
				"symbol":       symbol,
				"currentPrice": currentPrice,
				"prediction":   prediction,
				"timestamp":    time.Now().Unix(),
			}
			
			if err := conn.WriteJSON(data); err != nil {
				return
			}
		}
	}
}

// 获取历史数据API
func (h *Handler) GetHistoricalData(c *gin.Context) {
	symbol := c.DefaultQuery("symbol", "BTCUSDT")
	interval := c.DefaultQuery("interval", "1m")
	limitStr := c.DefaultQuery("limit", "100")
	
	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		limit = 100
	}
	
	klines, err := h.binanceService.GetHistoricalKlines(symbol, interval, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusOK, klines)
}

// 获取准确率统计
func (h *Handler) GetAccuracyStats(c *gin.Context) {
	symbol := c.DefaultQuery("symbol", "BTCUSDT")
	
	// 这里应该实现准确率统计逻辑
	// 比较历史预测结果与实际价格
	
	stats := map[string]interface{}{
		"symbol":           symbol,
		"total_predictions": 100,
		"accurate_predictions": 82,
		"accuracy_rate":    0.82,
		"avg_error":        0.15,
		"last_updated":     time.Now().Unix(),
	}
	
	c.JSON(http.StatusOK, stats)
}

// 获取交易建议
func (h *Handler) GetTradingAdvice(c *gin.Context) {
	symbol := c.DefaultQuery("symbol", "BTCUSDT")
	
	prediction, _ := h.predictionService.GetPrediction(symbol)
	technical, _ := h.analysisService.CalculateTechnicalIndicators(symbol, "1m")
	sentiment, _ := h.sentimentService.GetSentimentAnalysis(symbol, 24)
	
	advice := map[string]interface{}{
		"symbol":       symbol,
		"action":       "HOLD",
		"confidence":   0.75,
		"entry_price":  0.0,
		"stop_loss":    0.0,
		"take_profit":  0.0,
		"risk_level":   "MEDIUM",
		"reasoning":    "综合技术分析和情绪分析，建议持有观望",
		"prediction":   prediction,
		"technical":    technical,
		"sentiment":    sentiment,
		"timestamp":    time.Now().Unix(),
	}
	
	// 根据预测结果生成交易建议
	if prediction != nil {
		if prediction.Confidence > 0.7 {
			if prediction.Direction == "UP" && prediction.PercentChange > 0.2 {
				advice["action"] = "BUY"
				advice["entry_price"] = prediction.CurrentPrice
				advice["stop_loss"] = prediction.CurrentPrice * 0.98
				advice["take_profit"] = prediction.PredictedPrice
				advice["reasoning"] = "预测显示强烈上涨信号，建议买入"
			} else if prediction.Direction == "DOWN" && prediction.PercentChange < -0.2 {
				advice["action"] = "SELL"
				advice["reasoning"] = "预测显示强烈下跌信号，建议卖出"
			}
		}
	}
	
	c.JSON(http.StatusOK, advice)
}
