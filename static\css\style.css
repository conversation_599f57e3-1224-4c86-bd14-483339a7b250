/* KownBit 币安期货预判系统 - 样式文件 */

:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #00ff88;
    --danger-color: #ff4757;
    --warning-color: #ffd700;
    --info-color: #3742fa;
    --dark-color: #2f3542;
    --light-color: #f1f2f6;
    --gradient-bg: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--gradient-bg);
    color: #fff;
    min-height: 100vh;
    line-height: 1.6;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

.fade-in {
    animation: fadeIn 0.6s ease-out;
}

.pulse {
    animation: pulse 2s infinite;
}

.slide-in {
    animation: slideIn 0.5s ease-out;
}

/* 卡片样式 */
.card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
}

/* 按钮样式 */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1em;
    font-weight: 600;
    text-decoration: none;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: white;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.btn-success {
    background: linear-gradient(45deg, #00b894, #00cec9);
}

.btn-danger {
    background: linear-gradient(45deg, #e17055, #d63031);
}

.btn-warning {
    background: linear-gradient(45deg, #fdcb6e, #e84393);
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
    animation: pulse 2s infinite;
}

.status-online {
    background: var(--success-color);
    box-shadow: 0 0 10px var(--success-color);
}

.status-offline {
    background: var(--danger-color);
    box-shadow: 0 0 10px var(--danger-color);
}

.status-warning {
    background: var(--warning-color);
    box-shadow: 0 0 10px var(--warning-color);
}

/* 进度条 */
.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--danger-color), var(--warning-color), var(--success-color));
    border-radius: 4px;
    transition: width 0.3s ease;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* 数据显示 */
.data-value {
    font-size: 2em;
    font-weight: bold;
    margin: 10px 0;
}

.data-label {
    font-size: 0.9em;
    opacity: 0.8;
    margin-bottom: 5px;
}

.data-change {
    font-size: 1.1em;
    font-weight: 600;
}

.positive {
    color: var(--success-color);
}

.negative {
    color: var(--danger-color);
}

.neutral {
    color: var(--warning-color);
}

/* 图表容器 */
.chart-container {
    position: relative;
    height: 400px;
    margin: 20px 0;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 15px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .card {
        margin-bottom: 15px;
    }
    
    .data-value {
        font-size: 1.5em;
    }
    
    .chart-container {
        height: 300px;
    }
}

@media (max-width: 480px) {
    .data-value {
        font-size: 1.2em;
    }
    
    .chart-container {
        height: 250px;
    }
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 工具提示 */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 0.8em;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s;
    z-index: 1000;
}

.tooltip:hover::after {
    opacity: 1;
}

/* 通知样式 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 8px;
    padding: 15px 20px;
    border-left: 4px solid var(--success-color);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.notification.show {
    transform: translateX(0);
}

.notification.error {
    border-left-color: var(--danger-color);
}

.notification.warning {
    border-left-color: var(--warning-color);
}
