version: '3.8'

services:
  app:
    build: .
    ports:
      - "8080:8080"
    environment:
      - DATABASE_URL=***********************************/kownbit?sslmode=disable
      - REDIS_URL=redis://redis:6379
      - BINANCE_API_KEY=${BINANCE_API_KEY}
      - BINANCE_SECRET_KEY=${BINANCE_SECRET_KEY}
      - NEWS_API_KEY=${NEWS_API_KEY}
    depends_on:
      - db
      - redis
    volumes:
      - .:/app
    working_dir: /app

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=kownbit
      - POSTGRES_USER=kownbit
      - POSTGRES_PASSWORD=password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
