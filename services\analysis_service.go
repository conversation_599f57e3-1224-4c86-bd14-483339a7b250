package services

import (
	"math"
	"time"

	"kownbit/config"

	"github.com/montanaflynn/stats"
	"gorm.io/gorm"
)

type AnalysisService struct {
	db *gorm.DB
}

func NewAnalysisService(db *gorm.DB) *AnalysisService {
	return &AnalysisService{db: db}
}

// 技术指标结构
type TechnicalAnalysis struct {
	RSI            float64 `json:"rsi"`
	MACD           float64 `json:"macd"`
	MACDSignal     float64 `json:"macd_signal"`
	MACDHistogram  float64 `json:"macd_histogram"`
	MA20           float64 `json:"ma20"`
	MA50           float64 `json:"ma50"`
	EMA12          float64 `json:"ema12"`
	EMA26          float64 `json:"ema26"`
	BollingerUpper float64 `json:"bollinger_upper"`
	BollingerLower float64 `json:"bollinger_lower"`
	BollingerMid   float64 `json:"bollinger_mid"`
	VolumeMA       float64 `json:"volume_ma"`
	Signal         string  `json:"signal"`   // "BUY", "SELL", "HOLD"
	Strength       float64 `json:"strength"` // 信号强度 0-1
}

// 计算技术指标
func (s *AnalysisService) CalculateTechnicalIndicators(symbol string, interval string) (*TechnicalAnalysis, error) {
	// 获取历史数据
	klines, err := s.getKlineData(symbol, interval, 200)
	if err != nil {
		return nil, err
	}

	if len(klines) < 50 {
		return nil, nil // 数据不足
	}

	// 提取价格和成交量数据
	closes := make([]float64, len(klines))
	highs := make([]float64, len(klines))
	lows := make([]float64, len(klines))
	volumes := make([]float64, len(klines))

	for i, kline := range klines {
		closes[i] = kline.Close
		highs[i] = kline.High
		lows[i] = kline.Low
		volumes[i] = kline.Volume
	}

	analysis := &TechnicalAnalysis{}

	// 计算各种技术指标
	analysis.RSI = s.calculateRSI(closes, 14)
	analysis.MA20 = s.calculateSMA(closes, 20)
	analysis.MA50 = s.calculateSMA(closes, 50)
	analysis.EMA12 = s.calculateEMA(closes, 12)
	analysis.EMA26 = s.calculateEMA(closes, 26)

	macd, signal, histogram := s.calculateMACD(closes)
	analysis.MACD = macd
	analysis.MACDSignal = signal
	analysis.MACDHistogram = histogram

	upper, mid, lower := s.calculateBollingerBands(closes, 20, 2)
	analysis.BollingerUpper = upper
	analysis.BollingerMid = mid
	analysis.BollingerLower = lower

	analysis.VolumeMA = s.calculateSMA(volumes, 20)

	// 生成交易信号
	analysis.Signal, analysis.Strength = s.generateSignal(analysis, closes[len(closes)-1])

	// 保存到数据库
	s.saveTechnicalIndicator(symbol, analysis)

	return analysis, nil
}

// 计算RSI
func (s *AnalysisService) calculateRSI(prices []float64, period int) float64 {
	if len(prices) < period+1 {
		return 50 // 默认中性值
	}

	gains := make([]float64, 0)
	losses := make([]float64, 0)

	for i := 1; i < len(prices); i++ {
		change := prices[i] - prices[i-1]
		if change > 0 {
			gains = append(gains, change)
			losses = append(losses, 0)
		} else {
			gains = append(gains, 0)
			losses = append(losses, -change)
		}
	}

	if len(gains) < period {
		return 50
	}

	avgGain := s.calculateSMA(gains[len(gains)-period:], period)
	avgLoss := s.calculateSMA(losses[len(losses)-period:], period)

	if avgLoss == 0 {
		return 100
	}

	rs := avgGain / avgLoss
	rsi := 100 - (100 / (1 + rs))

	return rsi
}

// 计算简单移动平均线
func (s *AnalysisService) calculateSMA(prices []float64, period int) float64 {
	if len(prices) < period {
		return 0
	}

	sum := 0.0
	for i := len(prices) - period; i < len(prices); i++ {
		sum += prices[i]
	}

	return sum / float64(period)
}

// 计算指数移动平均线
func (s *AnalysisService) calculateEMA(prices []float64, period int) float64 {
	if len(prices) < period {
		return 0
	}

	multiplier := 2.0 / (float64(period) + 1.0)
	ema := prices[0]

	for i := 1; i < len(prices); i++ {
		ema = (prices[i] * multiplier) + (ema * (1 - multiplier))
	}

	return ema
}

// 计算MACD
func (s *AnalysisService) calculateMACD(prices []float64) (float64, float64, float64) {
	ema12 := s.calculateEMA(prices, 12)
	ema26 := s.calculateEMA(prices, 26)
	macd := ema12 - ema26

	// 计算信号线（MACD的9日EMA）
	macdLine := make([]float64, 0)
	for i := 26; i < len(prices); i++ {
		ema12_i := s.calculateEMA(prices[:i+1], 12)
		ema26_i := s.calculateEMA(prices[:i+1], 26)
		macdLine = append(macdLine, ema12_i-ema26_i)
	}

	signal := s.calculateEMA(macdLine, 9)
	histogram := macd - signal

	return macd, signal, histogram
}

// 计算布林带
func (s *AnalysisService) calculateBollingerBands(prices []float64, period int, stdDev float64) (float64, float64, float64) {
	if len(prices) < period {
		return 0, 0, 0
	}

	recentPrices := prices[len(prices)-period:]
	mean, _ := stats.Mean(recentPrices)
	stdDeviation, _ := stats.StandardDeviation(recentPrices)

	upper := mean + (stdDev * stdDeviation)
	lower := mean - (stdDev * stdDeviation)

	return upper, mean, lower
}

// 生成交易信号
func (s *AnalysisService) generateSignal(analysis *TechnicalAnalysis, currentPrice float64) (string, float64) {
	signals := make([]string, 0)
	strength := 0.0

	// RSI信号
	if analysis.RSI < 30 {
		signals = append(signals, "BUY")
		strength += 0.3
	} else if analysis.RSI > 70 {
		signals = append(signals, "SELL")
		strength += 0.3
	}

	// MACD信号
	if analysis.MACD > analysis.MACDSignal && analysis.MACDHistogram > 0 {
		signals = append(signals, "BUY")
		strength += 0.25
	} else if analysis.MACD < analysis.MACDSignal && analysis.MACDHistogram < 0 {
		signals = append(signals, "SELL")
		strength += 0.25
	}

	// 移动平均线信号
	if analysis.EMA12 > analysis.EMA26 && currentPrice > analysis.MA20 {
		signals = append(signals, "BUY")
		strength += 0.2
	} else if analysis.EMA12 < analysis.EMA26 && currentPrice < analysis.MA20 {
		signals = append(signals, "SELL")
		strength += 0.2
	}

	// 布林带信号
	if currentPrice < analysis.BollingerLower {
		signals = append(signals, "BUY")
		strength += 0.25
	} else if currentPrice > analysis.BollingerUpper {
		signals = append(signals, "SELL")
		strength += 0.25
	}

	// 统计信号
	buyCount := 0
	sellCount := 0
	for _, signal := range signals {
		if signal == "BUY" {
			buyCount++
		} else if signal == "SELL" {
			sellCount++
		}
	}

	if buyCount > sellCount {
		return "BUY", math.Min(strength, 1.0)
	} else if sellCount > buyCount {
		return "SELL", math.Min(strength, 1.0)
	}

	return "HOLD", strength
}

func (s *AnalysisService) getKlineData(symbol, interval string, limit int) ([]*config.KlineData, error) {
	var klines []*config.KlineData
	err := s.db.Where("symbol = ? AND interval = ?", symbol, interval).
		Order("open_time DESC").
		Limit(limit).
		Find(&klines).Error

	// 反转数组，使其按时间正序排列
	for i, j := 0, len(klines)-1; i < j; i, j = i+1, j-1 {
		klines[i], klines[j] = klines[j], klines[i]
	}

	return klines, err
}

func (s *AnalysisService) saveTechnicalIndicator(symbol string, analysis *TechnicalAnalysis) {
	indicator := &config.TechnicalIndicator{
		Symbol:         symbol,
		Timestamp:      time.Now().Unix(),
		RSI:            analysis.RSI,
		MACD:           analysis.MACD,
		MACDSignal:     analysis.MACDSignal,
		MA20:           analysis.MA20,
		MA50:           analysis.MA50,
		EMA12:          analysis.EMA12,
		EMA26:          analysis.EMA26,
		BollingerUpper: analysis.BollingerUpper,
		BollingerLower: analysis.BollingerLower,
	}

	s.db.Create(indicator)
}
