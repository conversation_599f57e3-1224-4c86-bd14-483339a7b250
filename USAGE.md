# KownBit 使用指南

## 🚀 快速开始

### 方法一：演示模式（推荐新手）
```bash
# Windows
demo.bat

# Linux/Mac
chmod +x scripts/setup.sh
./scripts/setup.sh
./scripts/run.sh
```

### 方法二：完整安装
1. **安装依赖**
   - Go 1.21+
   - PostgreSQL 12+
   - Redis 6+

2. **配置环境**
   ```bash
   cp .env.example .env
   # 编辑 .env 文件，填入真实的API密钥
   ```

3. **启动服务**
   ```bash
   go run main.go
   ```

4. **访问系统**
   - 打开浏览器访问: http://localhost:8080

## 📊 功能说明

### 主要功能
- **实时价格监控**: 显示BTC/ETH/BNB的实时价格
- **10分钟预测**: AI预测10分钟后的价格走势
- **技术分析**: RSI、MACD、移动平均线等指标
- **情绪分析**: 基于新闻和社交媒体的市场情绪
- **交易建议**: 综合分析后的买卖建议
- **准确率统计**: 实时跟踪预测准确率

### 预测算法
1. **线性回归** (权重40%): 基于历史价格趋势
2. **移动平均** (权重30%): 短期vs长期均线分析
3. **动量分析** (权重30%): 价格动量延续性
4. **综合调整**: 技术指标、情绪、成交量调整

### 技术指标解读
- **RSI < 30**: 超卖，可能反弹
- **RSI > 70**: 超买，可能回调
- **MACD金叉**: 买入信号
- **MACD死叉**: 卖出信号
- **价格突破布林带**: 趋势信号

## 🎯 准确率目标

### 设计目标
- **总体准确率**: 80%+
- **强信号准确率**: 85%+
- **平均误差**: <0.5%

### 提升策略
1. **数据质量**: 多源数据融合
2. **模型优化**: 机器学习持续训练
3. **风险控制**: 置信度阈值过滤
4. **实时调整**: 根据市场变化动态调整

## 📈 交易建议解读

### 信号类型
- **STRONG_BUY** 🚀: 强烈买入，置信度>80%
- **BUY** 🟢: 买入，置信度>70%
- **HOLD** ⏸️: 持有观望，置信度<70%
- **SELL** 🔴: 卖出，置信度>70%
- **STRONG_SELL** ⬇️: 强烈卖出，置信度>80%

### 风险等级
- **LOW**: 低风险，适合保守投资者
- **MEDIUM**: 中等风险，适合一般投资者
- **HIGH**: 高风险，适合激进投资者

## 🔧 配置说明

### 环境变量
```env
# 数据库配置
DATABASE_URL=postgres://user:pass@localhost/kownbit

# Redis缓存
REDIS_URL=redis://localhost:6379

# 币安API (必需)
BINANCE_API_KEY=your_api_key
BINANCE_SECRET_KEY=your_secret_key

# 新闻API (可选)
NEWS_API_KEY=your_news_api_key

# 服务器配置
PORT=8080
LOG_LEVEL=info
ENVIRONMENT=production
```

### 币安API申请
1. 登录币安账户
2. 进入"API管理"
3. 创建新API密钥
4. 设置权限：只需"读取"权限
5. 复制API Key和Secret到.env文件

## 📱 界面说明

### 主要区域
1. **价格显示**: 当前价格和24小时变化
2. **预测面板**: 10分钟后价格预测
3. **技术指标**: 各种技术分析指标
4. **市场情绪**: 基于新闻和社交媒体
5. **交易建议**: 综合分析的操作建议
6. **价格图表**: 实时价格走势图
7. **准确率统计**: 历史预测准确率

### 实时更新
- **价格**: 每秒更新
- **预测**: 每分钟更新
- **技术指标**: 每分钟更新
- **情绪分析**: 每5分钟更新

## ⚠️ 风险提示

### 重要声明
- 本系统仅供学习研究使用
- 预测结果不构成投资建议
- 数字货币投资存在高风险
- 请根据自身情况谨慎投资

### 使用建议
1. **小额测试**: 先用小额资金测试
2. **分散投资**: 不要把所有资金投入一个品种
3. **设置止损**: 严格执行止损策略
4. **理性投资**: 不要被情绪左右
5. **持续学习**: 不断提升投资知识

## 🐛 故障排除

### 常见问题
1. **连接失败**: 检查网络和API密钥
2. **数据不更新**: 检查币安API限制
3. **预测不准确**: 市场波动较大时正常
4. **页面加载慢**: 检查服务器性能

### 日志查看
```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
tail -f logs/error.log
```

## 📞 技术支持

### 获取帮助
- 查看README.md了解详细信息
- 提交Issue报告问题
- 参考API文档进行集成

### 联系方式
- GitHub Issues: 提交技术问题
- 邮件支持: 发送详细问题描述

---

**免责声明**: 本软件仅用于教育和研究目的。使用本软件进行实际交易的任何损失，开发者概不负责。投资有风险，入市需谨慎。
