# KownBit - 币安期货合约预判系统

🚀 基于AI的比特币价格预测系统，目标准确率80%+

## 功能特性

### 核心功能
- 📊 **实时数据采集**: 币安API集成，获取K线、深度、交易量数据
- 🔮 **智能预测**: 10分钟后价格预测，综合多种算法
- 📈 **技术分析**: RSI、MACD、移动平均线、布林带等指标
- 😊 **情绪分析**: 新闻舆情和社交媒体情绪监控
- 💡 **交易建议**: 基于综合分析的买卖建议
- 🎯 **准确率统计**: 实时跟踪预测准确率

### 技术特点
- **多模型融合**: 线性回归 + 移动平均 + 动量分析
- **实时更新**: WebSocket实时数据推送
- **响应式设计**: 支持桌面和移动端
- **高性能**: Redis缓存 + PostgreSQL存储
- **可扩展**: 模块化设计，易于添加新功能

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   数据采集层     │    │   分析处理层     │    │   展示交互层     │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ • 币安API       │    │ • 技术分析       │    │ • Web界面       │
│ • 新闻爬虫      │───▶│ • 情绪分析       │───▶│ • 实时图表       │
│ • 社交媒体      │    │ • 预测算法       │    │ • 交易建议       │
│ • 市场数据      │    │ • 风险评估       │    │ • 准确率统计     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   PostgreSQL    │    │     Redis       │    │   WebSocket     │
│   (历史数据)     │    │    (缓存)       │    │   (实时推送)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 快速开始

### 环境要求
- Go 1.21+
- PostgreSQL 12+
- Redis 6+

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd kownbit
```

2. **安装依赖**
```bash
go mod tidy
```

3. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，填入你的API密钥
```

4. **设置数据库**
```bash
# 创建PostgreSQL数据库
createdb kownbit

# 启动Redis
redis-server
```

5. **运行程序**
```bash
go run main.go
```

6. **访问系统**
打开浏览器访问: http://localhost:8080

## 配置说明

### 币安API配置
1. 登录币安账户
2. 进入API管理页面
3. 创建新的API密钥
4. 将API Key和Secret填入.env文件

### 数据库配置
```env
DATABASE_URL=postgres://username:password@localhost/kownbit?sslmode=disable
```

### Redis配置
```env
REDIS_URL=redis://localhost:6379
```

## API接口

### 获取预测结果
```
GET /api/prediction?symbol=BTCUSDT
```

### 获取技术分析
```
GET /api/analysis?symbol=BTCUSDT&interval=1m
```

### 获取情绪分析
```
GET /api/sentiment?symbol=BTCUSDT&hours=24
```

### 获取交易建议
```
GET /api/trading-advice?symbol=BTCUSDT
```

### WebSocket连接
```
ws://localhost:8080/ws?symbol=BTCUSDT
```

## 预测算法

### 1. 线性回归预测
- 使用最近20个数据点
- 基于时间序列的线性趋势
- 权重: 40%

### 2. 移动平均预测
- 短期MA(5) vs 长期MA(10)
- 基于趋势方向预测
- 权重: 30%

### 3. 动量预测
- 计算最近5个周期的价格动量
- 基于动量延续性预测
- 权重: 30%

### 4. 综合调整
- 技术指标调整: ±10%
- 情绪分析调整: ±5%
- 成交量分析调整: ±5%

## 技术指标

### RSI (相对强弱指数)
- 超买: RSI > 70 (卖出信号)
- 超卖: RSI < 30 (买入信号)
- 中性: 30 ≤ RSI ≤ 70

### MACD
- 金叉: MACD > Signal (买入信号)
- 死叉: MACD < Signal (卖出信号)

### 布林带
- 价格触及上轨: 卖出信号
- 价格触及下轨: 买入信号

### 移动平均线
- 短期MA > 长期MA: 上涨趋势
- 短期MA < 长期MA: 下跌趋势

## 情绪分析

### 数据源
- 新闻网站: CoinDesk, CoinTelegraph
- 社交媒体: Twitter, Reddit (模拟)
- 官方公告: 币安公告

### 分析方法
- 关键词匹配
- 情绪词典评分
- 趋势变化检测

## 风险提示

⚠️ **重要声明**:
- 本系统仅供学习和研究使用
- 预测结果不构成投资建议
- 数字货币投资存在高风险
- 请根据自身情况谨慎投资
- 过往表现不代表未来收益

## 开发计划

### v1.0 (当前版本)
- ✅ 基础预测功能
- ✅ 技术指标分析
- ✅ 情绪分析
- ✅ Web界面

### v1.1 (计划中)
- 🔄 机器学习模型优化
- 🔄 更多技术指标
- 🔄 历史回测功能
- 🔄 移动端APP

### v1.2 (未来)
- 📋 多交易所支持
- 📋 自动交易功能
- 📋 策略回测
- 📋 用户系统

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请提交Issue或联系开发团队。

---

**免责声明**: 本软件仅用于教育和研究目的。使用本软件进行实际交易的任何损失，开发者概不负责。
