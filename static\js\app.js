// KownBit 币安期货预判系统 - 主要JavaScript文件

class KownBitApp {
    constructor() {
        this.currentSymbol = 'BTCUSDT';
        this.ws = null;
        this.priceChart = null;
        this.isConnected = false;
        this.retryCount = 0;
        this.maxRetries = 5;
        
        this.init();
    }

    init() {
        console.log('🚀 KownBit系统初始化...');
        
        // 绑定事件
        this.bindEvents();
        
        // 初始化图表
        this.initChart();
        
        // 连接WebSocket
        this.connectWebSocket();
        
        // 加载初始数据
        this.loadInitialData();
        
        // 设置定时刷新
        this.setupAutoRefresh();
        
        console.log('✅ 系统初始化完成');
    }

    bindEvents() {
        // 交易对选择
        const symbolSelect = document.getElementById('symbolSelect');
        if (symbolSelect) {
            symbolSelect.addEventListener('change', (e) => {
                this.changeSymbol(e.target.value);
            });
        }

        // 刷新按钮
        const refreshBtn = document.querySelector('.refresh-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.refreshData();
            });
        }

        // 页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.disconnect();
            } else {
                this.connectWebSocket();
            }
        });
    }

    initChart() {
        const ctx = document.getElementById('priceChart');
        if (!ctx) return;

        this.priceChart = new Chart(ctx.getContext('2d'), {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: '实际价格',
                    data: [],
                    borderColor: '#00ff88',
                    backgroundColor: 'rgba(0, 255, 136, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: '预测价格',
                    data: [],
                    borderColor: '#ffd700',
                    backgroundColor: 'rgba(255, 215, 0, 0.1)',
                    borderDash: [5, 5],
                    tension: 0.4,
                    fill: false
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                plugins: {
                    legend: {
                        labels: {
                            color: '#fff',
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: '#fff',
                        borderWidth: 1
                    }
                },
                scales: {
                    x: {
                        ticks: { 
                            color: '#fff',
                            maxTicksLimit: 10
                        },
                        grid: { 
                            color: 'rgba(255, 255, 255, 0.1)',
                            drawBorder: false
                        }
                    },
                    y: {
                        ticks: { 
                            color: '#fff',
                            callback: function(value) {
                                return '$' + value.toFixed(2);
                            }
                        },
                        grid: { 
                            color: 'rgba(255, 255, 255, 0.1)',
                            drawBorder: false
                        }
                    }
                },
                animation: {
                    duration: 1000,
                    easing: 'easeInOutQuart'
                }
            }
        });
    }

    connectWebSocket() {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            return;
        }

        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws?symbol=${this.currentSymbol}`;
        
        console.log('🔌 连接WebSocket:', wsUrl);
        
        this.ws = new WebSocket(wsUrl);
        
        this.ws.onopen = () => {
            console.log('✅ WebSocket连接成功');
            this.isConnected = true;
            this.retryCount = 0;
            this.updateConnectionStatus(true);
        };
        
        this.ws.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                this.handleWebSocketMessage(data);
            } catch (error) {
                console.error('❌ WebSocket消息解析错误:', error);
            }
        };
        
        this.ws.onclose = () => {
            console.log('🔌 WebSocket连接关闭');
            this.isConnected = false;
            this.updateConnectionStatus(false);
            this.scheduleReconnect();
        };
        
        this.ws.onerror = (error) => {
            console.error('❌ WebSocket错误:', error);
            this.isConnected = false;
            this.updateConnectionStatus(false);
        };
    }

    handleWebSocketMessage(data) {
        switch (data.type) {
            case 'price_update':
                this.updatePrice(data);
                break;
            case 'prediction_update':
                this.updatePrediction(data);
                break;
            case 'analysis_update':
                this.updateAnalysis(data);
                break;
            default:
                console.log('📨 收到未知消息类型:', data.type);
        }
    }

    updatePrice(data) {
        const priceElement = document.getElementById('currentPrice');
        if (priceElement && data.currentPrice) {
            const oldPrice = parseFloat(priceElement.textContent.replace('$', ''));
            const newPrice = data.currentPrice;
            
            priceElement.textContent = `$${newPrice.toFixed(2)}`;
            
            // 价格变化动画
            if (oldPrice !== newPrice) {
                priceElement.classList.remove('positive', 'negative');
                if (newPrice > oldPrice) {
                    priceElement.classList.add('positive');
                } else if (newPrice < oldPrice) {
                    priceElement.classList.add('negative');
                }
                
                // 添加脉冲动画
                priceElement.classList.add('pulse');
                setTimeout(() => {
                    priceElement.classList.remove('pulse');
                }, 1000);
            }
        }

        // 更新图表
        if (this.priceChart && data.currentPrice) {
            this.addDataToChart(data.currentPrice, data.prediction?.predicted_price);
        }
    }

    updatePrediction(data) {
        if (!data.prediction) return;

        const prediction = data.prediction;
        
        // 更新预测价格
        const predPriceElement = document.querySelector('.prediction-box .price');
        if (predPriceElement) {
            predPriceElement.textContent = `$${prediction.predicted_price.toFixed(2)}`;
        }

        // 更新方向和变化
        const directionElement = document.querySelector('.prediction-box .direction');
        if (directionElement) {
            directionElement.textContent = `${prediction.direction} ${prediction.percent_change.toFixed(2)}%`;
            directionElement.className = prediction.direction === 'UP' ? 'positive' : 
                                       prediction.direction === 'DOWN' ? 'negative' : 'neutral';
        }

        // 更新置信度
        const confidenceBar = document.querySelector('.confidence-fill');
        if (confidenceBar) {
            confidenceBar.style.width = `${(prediction.confidence * 100).toFixed(0)}%`;
        }
    }

    addDataToChart(actualPrice, predictedPrice) {
        if (!this.priceChart) return;

        const now = new Date();
        const timeLabel = now.toLocaleTimeString();

        // 限制数据点数量
        const maxDataPoints = 50;
        
        if (this.priceChart.data.labels.length >= maxDataPoints) {
            this.priceChart.data.labels.shift();
            this.priceChart.data.datasets[0].data.shift();
            this.priceChart.data.datasets[1].data.shift();
        }

        this.priceChart.data.labels.push(timeLabel);
        this.priceChart.data.datasets[0].data.push(actualPrice);
        this.priceChart.data.datasets[1].data.push(predictedPrice || null);

        this.priceChart.update('none');
    }

    scheduleReconnect() {
        if (this.retryCount >= this.maxRetries) {
            console.log('❌ 达到最大重连次数，停止重连');
            this.showNotification('连接失败，请刷新页面', 'error');
            return;
        }

        const delay = Math.min(1000 * Math.pow(2, this.retryCount), 30000);
        console.log(`🔄 ${delay/1000}秒后重连... (${this.retryCount + 1}/${this.maxRetries})`);
        
        setTimeout(() => {
            this.retryCount++;
            this.connectWebSocket();
        }, delay);
    }

    updateConnectionStatus(connected) {
        const indicators = document.querySelectorAll('.status-indicator');
        indicators.forEach(indicator => {
            indicator.className = `status-indicator ${connected ? 'status-online' : 'status-offline'}`;
        });
    }

    disconnect() {
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
    }

    changeSymbol(symbol) {
        this.currentSymbol = symbol;
        this.disconnect();
        window.location.href = `/?symbol=${symbol}`;
    }

    refreshData() {
        console.log('🔄 刷新数据...');
        location.reload();
    }

    loadInitialData() {
        // 加载历史数据
        this.loadHistoricalData();
        
        // 加载交易建议
        this.loadTradingAdvice();
        
        // 加载准确率统计
        this.loadAccuracyStats();
    }

    async loadHistoricalData() {
        try {
            const response = await fetch(`/api/historical?symbol=${this.currentSymbol}&interval=1m&limit=50`);
            const data = await response.json();
            
            if (data && data.length > 0) {
                const labels = data.map(item => new Date(item.OpenTime).toLocaleTimeString());
                const prices = data.map(item => item.Close);
                
                if (this.priceChart) {
                    this.priceChart.data.labels = labels;
                    this.priceChart.data.datasets[0].data = prices;
                    this.priceChart.update();
                }
            }
        } catch (error) {
            console.error('❌ 加载历史数据失败:', error);
        }
    }

    async loadTradingAdvice() {
        try {
            const response = await fetch(`/api/trading-advice?symbol=${this.currentSymbol}`);
            const advice = await response.json();
            
            const adviceElement = document.getElementById('tradingAdvice');
            if (adviceElement && advice) {
                const actionClass = this.getActionClass(advice.action);
                const emoji = this.getActionEmoji(advice.action);
                
                adviceElement.innerHTML = `
                    <div class="advice-action ${actionClass}">${emoji} ${advice.action}</div>
                    <div class="advice-reasoning">${advice.reasoning}</div>
                    <div style="margin-top: 10px; font-size: 0.9em;">
                        置信度: ${(advice.confidence * 100).toFixed(1)}% | 风险等级: ${advice.risk_level}
                    </div>
                `;
            }
        } catch (error) {
            console.error('❌ 加载交易建议失败:', error);
        }
    }

    async loadAccuracyStats() {
        try {
            const response = await fetch(`/api/accuracy-stats?symbol=${this.currentSymbol}`);
            const stats = await response.json();
            
            if (stats) {
                this.updateElement('totalPredictions', stats.total_predictions);
                this.updateElement('accuratePredictions', stats.accurate_predictions);
                this.updateElement('accuracyRate', `${(stats.accuracy_rate * 100).toFixed(1)}%`);
                this.updateElement('avgError', `${(stats.avg_error * 100).toFixed(2)}%`);
            }
        } catch (error) {
            console.error('❌ 加载准确率统计失败:', error);
        }
    }

    updateElement(id, value) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
        }
    }

    getActionClass(action) {
        const classes = {
            'BUY': 'positive',
            'STRONG_BUY': 'positive',
            'SELL': 'negative',
            'STRONG_SELL': 'negative',
            'HOLD': 'neutral'
        };
        return classes[action] || 'neutral';
    }

    getActionEmoji(action) {
        const emojis = {
            'BUY': '🟢',
            'STRONG_BUY': '🚀',
            'SELL': '🔴',
            'STRONG_SELL': '⬇️',
            'HOLD': '⏸️'
        };
        return emojis[action] || '❓';
    }

    setupAutoRefresh() {
        // 每5分钟刷新一次非实时数据
        setInterval(() => {
            this.loadTradingAdvice();
            this.loadAccuracyStats();
        }, 5 * 60 * 1000);
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        // 显示通知
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);
        
        // 自动隐藏
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
}

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.kownbitApp = new KownBitApp();
});

// 全局函数（保持向后兼容）
function changeSymbol() {
    const select = document.getElementById('symbolSelect');
    if (select && window.kownbitApp) {
        window.kownbitApp.changeSymbol(select.value);
    }
}

function refreshData() {
    if (window.kownbitApp) {
        window.kownbitApp.refreshData();
    }
}
