package config

import (
	"fmt"
	"log"
	"os"
	"strings"

	"github.com/go-redis/redis/v8"
	"gorm.io/driver/postgres"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

type Config struct {
	DatabaseURL      string
	RedisURL         string
	BinanceAPIKey    string
	BinanceSecretKey string
	NewsAPIKey       string
	Port             string
	DemoMode         bool
}

func Load() *Config {
	return &Config{
		DatabaseURL:      getEnv("DATABASE_URL", "sqlite://kownbit.db"),
		RedisURL:         getEnv("REDIS_URL", "redis://localhost:6379"),
		BinanceAPIKey:    getEnv("BINANCE_API_KEY", "demo_api_key"),
		BinanceSecretKey: getEnv("BINANCE_SECRET_KEY", "demo_secret_key"),
		NewsAPIKey:       getEnv("NEWS_API_KEY", "demo_news_key"),
		Port:             getEnv("PORT", "8080"),
		DemoMode:         getEnv("DEMO_MODE", "true") == "true",
	}
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func InitDB(cfg *Config) (*gorm.DB, error) {
	var db *gorm.DB
	var err error

	// 判断数据库类型
	if strings.HasPrefix(cfg.DatabaseURL, "sqlite://") {
		// 使用SQLite
		dbPath := strings.TrimPrefix(cfg.DatabaseURL, "sqlite://")
		log.Printf("使用SQLite数据库: %s", dbPath)
		db, err = gorm.Open(sqlite.Open(dbPath), &gorm.Config{})
	} else {
		// 使用PostgreSQL
		log.Printf("使用PostgreSQL数据库")
		db, err = gorm.Open(postgres.Open(cfg.DatabaseURL), &gorm.Config{})
	}

	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	log.Printf("数据库连接成功")

	// 自动迁移
	err = db.AutoMigrate(
		&KlineData{},
		&PredictionResult{},
		&SentimentData{},
		&TechnicalIndicator{},
	)
	if err != nil {
		return nil, fmt.Errorf("failed to migrate database: %w", err)
	}

	log.Printf("数据库迁移完成")

	// 创建索引以提高查询性能
	createIndexes(db)

	return db, nil
}

// 创建数据库索引
func createIndexes(db *gorm.DB) {
	// KlineData表索引
	db.Exec("CREATE INDEX IF NOT EXISTS idx_kline_symbol_interval_time ON kline_data(symbol, interval, open_time)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_kline_symbol_time ON kline_data(symbol, open_time)")

	// PredictionResult表索引
	db.Exec("CREATE INDEX IF NOT EXISTS idx_prediction_symbol_time ON prediction_results(symbol, prediction_time)")

	// SentimentData表索引
	db.Exec("CREATE INDEX IF NOT EXISTS idx_sentiment_symbol_time ON sentiment_data(symbol, timestamp)")

	// TechnicalIndicator表索引
	db.Exec("CREATE INDEX IF NOT EXISTS idx_technical_symbol_time ON technical_indicators(symbol, timestamp)")

	log.Printf("数据库索引创建完成")
}

func InitRedis(cfg *Config) *redis.Client {
	if cfg.DemoMode {
		log.Printf("演示模式：跳过Redis连接")
		return nil
	}

	opt, err := redis.ParseURL(cfg.RedisURL)
	if err != nil {
		log.Printf("Redis URL解析失败: %v，使用默认配置", err)
		opt = &redis.Options{
			Addr: "localhost:6379",
		}
	}

	client := redis.NewClient(opt)

	// 测试连接
	_, err = client.Ping(client.Context()).Result()
	if err != nil {
		log.Printf("Redis连接失败: %v，将使用内存缓存", err)
		return nil
	}

	log.Printf("Redis连接成功")
	return client
}

// 数据模型
type KlineData struct {
	ID          uint   `gorm:"primaryKey"`
	Symbol      string `gorm:"index"`
	OpenTime    int64  `gorm:"index"`
	CloseTime   int64
	Open        float64
	High        float64
	Low         float64
	Close       float64
	Volume      float64
	QuoteVolume float64
	Interval    string `gorm:"index"`
}

type PredictionResult struct {
	ID             uint   `gorm:"primaryKey"`
	Symbol         string `gorm:"index"`
	PredictedPrice float64
	CurrentPrice   float64
	Confidence     float64
	Direction      string   // "UP", "DOWN", "SIDEWAYS"
	PredictionTime int64    `gorm:"index"`
	TargetTime     int64    // 预测目标时间（10分钟后）
	ActualPrice    *float64 // 实际价格（用于验证准确率）
	IsAccurate     *bool    // 预测是否准确
}

type SentimentData struct {
	ID        uint   `gorm:"primaryKey"`
	Source    string // "twitter", "reddit", "news"
	Content   string
	Sentiment float64 // -1 到 1，负数表示负面，正数表示正面
	Timestamp int64   `gorm:"index"`
	Symbol    string  `gorm:"index"`
}

type TechnicalIndicator struct {
	ID             uint   `gorm:"primaryKey"`
	Symbol         string `gorm:"index"`
	Timestamp      int64  `gorm:"index"`
	RSI            float64
	MACD           float64
	MACDSignal     float64
	MA20           float64
	MA50           float64
	EMA12          float64
	EMA26          float64
	BollingerUpper float64
	BollingerLower float64
	Volume         float64
}
