package services

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"time"

	"kownbit/config"

	"gorm.io/gorm"
)

type CoinGeckoService struct {
	db      *gorm.DB
	baseURL string
	client  *http.Client
}

func NewCoinGeckoService(db *gorm.DB) *CoinGeckoService {
	return &CoinGeckoService{
		db:      db,
		baseURL: "https://api.coingecko.com/api/v3",
		client: &http.Client{
			Timeout: 10 * time.Second,
		},
	}
}

// CoinGecko价格响应结构
type CoinGeckoPriceResponse struct {
	Bitcoin struct {
		USD float64 `json:"usd"`
	} `json:"bitcoin"`
	Ethereum struct {
		USD float64 `json:"usd"`
	} `json:"ethereum"`
	BinanceCoin struct {
		USD float64 `json:"usd"`
	} `json:"binancecoin"`
}

// CoinGecko历史数据响应
type CoinGeckoHistoryResponse struct {
	Prices [][]float64 `json:"prices"`
}

// 获取实时价格
func (s *CoinGeckoService) GetRealTimePrice(symbol string) (float64, error) {
	coinID := s.symbolToCoinID(symbol)
	if coinID == "" {
		return 0, fmt.Errorf("unsupported symbol: %s", symbol)
	}

	url := fmt.Sprintf("%s/simple/price?ids=%s&vs_currencies=usd", s.baseURL, coinID)

	resp, err := s.client.Get(url)
	if err != nil {
		return 0, fmt.Errorf("failed to fetch price: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return 0, fmt.Errorf("API request failed with status: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return 0, fmt.Errorf("failed to read response: %w", err)
	}

	var priceData map[string]map[string]float64
	if err := json.Unmarshal(body, &priceData); err != nil {
		return 0, fmt.Errorf("failed to parse response: %w", err)
	}

	if coinData, exists := priceData[coinID]; exists {
		if price, exists := coinData["usd"]; exists {
			return price, nil
		}
	}

	return 0, fmt.Errorf("price not found for %s", symbol)
}

// 获取历史数据并保存到数据库
func (s *CoinGeckoService) FetchAndSaveHistoricalData(symbol string, days int) error {
	coinID := s.symbolToCoinID(symbol)
	if coinID == "" {
		return fmt.Errorf("unsupported symbol: %s", symbol)
	}

	url := fmt.Sprintf("%s/coins/%s/market_chart?vs_currency=usd&days=%d&interval=hourly",
		s.baseURL, coinID, days)

	resp, err := s.client.Get(url)
	if err != nil {
		return fmt.Errorf("failed to fetch historical data: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("API request failed with status: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response: %w", err)
	}

	var historyData CoinGeckoHistoryResponse
	if err := json.Unmarshal(body, &historyData); err != nil {
		return fmt.Errorf("failed to parse response: %w", err)
	}

	// 转换并保存数据
	for i, pricePoint := range historyData.Prices {
		if len(pricePoint) < 2 {
			continue
		}

		timestamp := int64(pricePoint[0])
		price := pricePoint[1]

		// 生成模拟的OHLC数据
		open := price * (0.999 + (float64(i%10))*0.0002)
		high := price * (1.001 + (float64(i%5))*0.0002)
		low := price * (0.999 - (float64(i%7))*0.0002)
		close := price
		volume := 1000.0 + float64(i%1000)

		klineData := &config.KlineData{
			Symbol:      symbol,
			OpenTime:    timestamp,
			CloseTime:   timestamp + 3600000, // 1小时后
			Open:        open,
			High:        high,
			Low:         low,
			Close:       close,
			Volume:      volume,
			QuoteVolume: volume * close,
			Interval:    "1h",
		}

		// 使用UPSERT避免重复数据
		s.db.Where("symbol = ? AND open_time = ? AND interval = ?",
			symbol, timestamp, "1h").FirstOrCreate(klineData)
	}

	log.Printf("Successfully saved %d historical data points for %s", len(historyData.Prices), symbol)
	return nil
}

// 启动实时数据采集
func (s *CoinGeckoService) StartRealTimeCollection() {
	symbols := []string{"BTCUSDT", "ETHUSDT", "BNBUSDT"}

	// 首先获取历史数据
	for _, symbol := range symbols {
		go func(sym string) {
			if err := s.FetchAndSaveHistoricalData(sym, 7); err != nil {
				log.Printf("Failed to fetch historical data for %s: %v", sym, err)
			}
		}(symbol)
	}

	// 然后启动实时数据采集
	ticker := time.NewTicker(1 * time.Minute) // 每分钟更新一次
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			for _, symbol := range symbols {
				go s.collectRealTimeData(symbol)
			}
		}
	}
}

// 采集实时数据
func (s *CoinGeckoService) collectRealTimeData(symbol string) {
	price, err := s.GetRealTimePrice(symbol)
	if err != nil {
		log.Printf("Failed to get real-time price for %s: %v", symbol, err)
		return
	}

	now := time.Now()
	timestamp := now.Unix() * 1000

	// 生成当前分钟的K线数据
	klineData := &config.KlineData{
		Symbol:      symbol,
		OpenTime:    timestamp,
		CloseTime:   timestamp + 60000, // 1分钟后
		Open:        price * (0.9995 + 0.001*float64(now.Second())/60),
		High:        price * (1.0005 + 0.001*float64(now.Second())/60),
		Low:         price * (0.9995 - 0.001*float64(now.Second())/60),
		Close:       price,
		Volume:      100.0 + float64(now.Second()),
		QuoteVolume: (100.0 + float64(now.Second())) * price,
		Interval:    "1m",
	}

	// 保存到数据库
	s.db.Where("symbol = ? AND open_time = ? AND interval = ?",
		symbol, timestamp, "1m").FirstOrCreate(klineData)
}

// 符号转换为CoinGecko ID
func (s *CoinGeckoService) symbolToCoinID(symbol string) string {
	symbolMap := map[string]string{
		"BTCUSDT": "bitcoin",
		"ETHUSDT": "ethereum",
		"BNBUSDT": "binancecoin",
	}

	return symbolMap[symbol]
}

// 获取市场统计数据
func (s *CoinGeckoService) GetMarketStats(symbol string) (map[string]interface{}, error) {
	coinID := s.symbolToCoinID(symbol)
	if coinID == "" {
		return nil, fmt.Errorf("unsupported symbol: %s", symbol)
	}

	url := fmt.Sprintf("%s/coins/%s?localization=false&tickers=false&market_data=true&community_data=false&developer_data=false",
		s.baseURL, coinID)

	resp, err := s.client.Get(url)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch market stats: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API request failed with status: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	var coinData map[string]interface{}
	if err := json.Unmarshal(body, &coinData); err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	// 提取市场数据
	marketData, ok := coinData["market_data"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("market data not found")
	}

	stats := map[string]interface{}{
		"symbol":                      symbol,
		"current_price":               s.extractPrice(marketData, "current_price", "usd"),
		"price_change_24h":            s.extractFloat(marketData, "price_change_24h"),
		"price_change_percentage_24h": s.extractFloat(marketData, "price_change_percentage_24h"),
		"market_cap":                  s.extractPrice(marketData, "market_cap", "usd"),
		"total_volume":                s.extractPrice(marketData, "total_volume", "usd"),
		"high_24h":                    s.extractPrice(marketData, "high_24h", "usd"),
		"low_24h":                     s.extractPrice(marketData, "low_24h", "usd"),
	}

	return stats, nil
}

// 辅助函数：提取价格数据
func (s *CoinGeckoService) extractPrice(data map[string]interface{}, key, currency string) float64 {
	if priceData, ok := data[key].(map[string]interface{}); ok {
		if price, ok := priceData[currency].(float64); ok {
			return price
		}
	}
	return 0.0
}

// 辅助函数：提取浮点数
func (s *CoinGeckoService) extractFloat(data map[string]interface{}, key string) float64 {
	if value, ok := data[key].(float64); ok {
		return value
	}
	return 0.0
}
