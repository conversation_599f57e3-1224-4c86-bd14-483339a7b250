package main

import (
	"html/template"
	"log"
	"net/http"
	"os"
	"strconv"

	"kownbit/config"
	"kownbit/handlers"
	"kownbit/services"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
)

func main() {
	// 加载环境变量
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found")
	}

	// 初始化配置
	cfg := config.Load()

	// 初始化数据库
	db, err := config.InitDB(cfg)
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	// 初始化Redis
	redis := config.InitRedis(cfg)

	// 初始化服务
	binanceService := services.NewBinanceService(cfg.BinanceAPIKey, cfg.BinanceSecretKey)
	binanceService.SetDB(db)
	coinGeckoService := services.NewCoinGeckoService(db)
	binanceService.SetCoinGeckoService(coinGeckoService)
	predictionService := services.NewPredictionService(db, redis)
	analysisService := services.NewAnalysisService(db)
	sentimentService := services.NewSentimentService()
	sentimentService.SetDB(db)

	// 启动数据采集
	go binanceService.StartDataCollection()
	// 暂时禁用CoinGecko以避免网络问题
	// go coinGeckoService.StartRealTimeCollection()
	go sentimentService.StartSentimentCollection()

	// 启动预测服务
	go predictionService.StartPredictionEngine()

	// 设置路由
	router := gin.Default()

	// 添加模板函数
	router.SetFuncMap(template.FuncMap{
		"mul": func(a, b float64) float64 {
			return a * b
		},
		"parseFloat": func(s interface{}) float64 {
			switch v := s.(type) {
			case string:
				if f, err := strconv.ParseFloat(v, 64); err == nil {
					return f
				}
			case float64:
				return v
			case float32:
				return float64(v)
			case int:
				return float64(v)
			case int64:
				return float64(v)
			}
			return 0.0
		},
	})

	// 加载HTML模板
	router.LoadHTMLGlob("templates/*")

	// 静态文件
	router.Static("/static", "./static")

	// 初始化处理器
	h := handlers.NewHandler(binanceService, predictionService, analysisService, sentimentService)

	// 路由设置
	router.GET("/", h.Dashboard)
	router.GET("/api/prediction", h.GetPrediction)
	router.GET("/api/analysis", h.GetAnalysis)
	router.GET("/api/sentiment", h.GetSentiment)
	router.GET("/api/historical", h.GetHistoricalData)
	router.GET("/api/accuracy-stats", h.GetAccuracyStats)
	router.GET("/api/trading-advice", h.GetTradingAdvice)
	router.GET("/ws", h.WebSocketHandler)

	// 启动服务器
	port := os.Getenv("PORT")
	if port == "" {
		port = "8080"
	}

	log.Printf("Server starting on port %s", port)
	log.Fatal(http.ListenAndServe(":"+port, router))
}
