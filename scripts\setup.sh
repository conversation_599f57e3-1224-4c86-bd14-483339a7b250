#!/bin/bash

echo "🚀 KownBit 币安期货预判系统 - 安装脚本"
echo "========================================"

# 检查Go版本
if ! command -v go &> /dev/null; then
    echo "❌ Go未安装，请先安装Go 1.21+"
    exit 1
fi

GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')
echo "✅ Go版本: $GO_VERSION"

# 检查PostgreSQL
if ! command -v psql &> /dev/null; then
    echo "⚠️  PostgreSQL未安装，请手动安装PostgreSQL 12+"
fi

# 检查Redis
if ! command -v redis-cli &> /dev/null; then
    echo "⚠️  Redis未安装，请手动安装Redis 6+"
fi

# 创建.env文件
if [ ! -f .env ]; then
    echo "📝 创建.env配置文件..."
    cp .env.example .env
    echo "✅ 请编辑.env文件，填入你的API密钥"
fi

# 安装Go依赖
echo "📦 安装Go依赖..."
go mod tidy

# 创建数据库（如果PostgreSQL可用）
if command -v createdb &> /dev/null; then
    echo "🗄️  创建数据库..."
    createdb kownbit 2>/dev/null || echo "数据库可能已存在"
fi

echo ""
echo "🎉 安装完成！"
echo ""
echo "下一步："
echo "1. 编辑 .env 文件，填入你的币安API密钥"
echo "2. 确保PostgreSQL和Redis正在运行"
echo "3. 运行: go run main.go"
echo "4. 访问: http://localhost:8080"
echo ""
echo "或者使用Docker："
echo "docker-compose up -d"
