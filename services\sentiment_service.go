package services

import (
	"strings"
	"time"

	"kownbit/config"

	"github.com/gocolly/colly/v2"
	"gorm.io/gorm"
)

type SentimentService struct {
	db *gorm.DB
}

func NewSentimentService() *SentimentService {
	return &SentimentService{}
}

func (s *SentimentService) SetDB(db *gorm.DB) {
	s.db = db
}

// 新闻数据结构
type NewsArticle struct {
	Title       string    `json:"title"`
	Description string    `json:"description"`
	URL         string    `json:"url"`
	PublishedAt time.Time `json:"publishedAt"`
	Source      string    `json:"source"`
}

type NewsResponse struct {
	Articles []NewsArticle `json:"articles"`
}

// 启动情绪数据收集
func (s *SentimentService) StartSentimentCollection() {
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			go s.collectNewsData()
			go s.collectSocialData()
		}
	}
}

// 收集新闻数据
func (s *SentimentService) collectNewsData() {
	keywords := []string{"bitcoin", "BTC", "cryptocurrency", "crypto"}

	for _, keyword := range keywords {
		articles := s.fetchNewsArticles(keyword)
		for _, article := range articles {
			sentiment := s.analyzeSentiment(article.Title + " " + article.Description)

			sentimentData := &config.SentimentData{
				Source:    "news",
				Content:   article.Title + " " + article.Description,
				Sentiment: sentiment,
				Timestamp: time.Now().Unix(),
				Symbol:    "BTCUSDT",
			}

			if s.db != nil {
				s.db.Create(sentimentData)
			}
		}
	}
}

// 获取新闻文章
func (s *SentimentService) fetchNewsArticles(keyword string) []NewsArticle {
	articles := make([]NewsArticle, 0)

	// 使用爬虫获取新闻数据
	c := colly.NewCollector()

	// 设置用户代理
	c.UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"

	// 爬取CoinDesk
	c.OnHTML(".articleTextSection", func(e *colly.HTMLElement) {
		title := e.ChildText("h1")
		description := e.ChildText("p")

		if title != "" && strings.Contains(strings.ToLower(title+description), strings.ToLower(keyword)) {
			article := NewsArticle{
				Title:       title,
				Description: description,
				Source:      "coindesk",
				PublishedAt: time.Now(),
			}
			articles = append(articles, article)
		}
	})

	// 访问CoinDesk新闻页面
	c.Visit("https://www.coindesk.com/")

	// 爬取CoinTelegraph
	c2 := colly.NewCollector()
	c2.UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"

	c2.OnHTML(".post-card", func(e *colly.HTMLElement) {
		title := e.ChildText(".post-card__title")
		description := e.ChildText(".post-card__text")

		if title != "" && strings.Contains(strings.ToLower(title+description), strings.ToLower(keyword)) {
			article := NewsArticle{
				Title:       title,
				Description: description,
				Source:      "cointelegraph",
				PublishedAt: time.Now(),
			}
			articles = append(articles, article)
		}
	})

	c2.Visit("https://cointelegraph.com/")

	return articles
}

// 收集社交媒体数据
func (s *SentimentService) collectSocialData() {
	// 模拟社交媒体数据收集
	// 在实际应用中，这里会连接到Twitter API、Reddit API等

	socialPosts := []string{
		"Bitcoin is going to the moon! 🚀",
		"BTC looking bearish today, might be a good time to sell",
		"Crypto market is so volatile, hard to predict",
		"Bitcoin adoption is increasing, bullish long term",
		"Market manipulation is obvious, be careful",
	}

	for _, post := range socialPosts {
		sentiment := s.analyzeSentiment(post)

		sentimentData := &config.SentimentData{
			Source:    "social",
			Content:   post,
			Sentiment: sentiment,
			Timestamp: time.Now().Unix(),
			Symbol:    "BTCUSDT",
		}

		if s.db != nil {
			s.db.Create(sentimentData)
		}
	}
}

// 分析情绪
func (s *SentimentService) analyzeSentiment(text string) float64 {
	// 简化的情绪分析算法
	// 在实际应用中，应该使用更复杂的NLP模型

	text = strings.ToLower(text)

	// 正面词汇
	positiveWords := []string{
		"bull", "bullish", "moon", "pump", "rise", "up", "gain", "profit",
		"buy", "long", "positive", "good", "great", "excellent", "amazing",
		"rocket", "surge", "rally", "breakout", "support", "strong",
	}

	// 负面词汇
	negativeWords := []string{
		"bear", "bearish", "dump", "crash", "fall", "down", "loss", "lose",
		"sell", "short", "negative", "bad", "terrible", "awful", "drop",
		"decline", "resistance", "weak", "fear", "panic",
	}

	positiveCount := 0
	negativeCount := 0

	// 计算正面和负面词汇出现次数
	for _, word := range positiveWords {
		if strings.Contains(text, word) {
			positiveCount++
		}
	}

	for _, word := range negativeWords {
		if strings.Contains(text, word) {
			negativeCount++
		}
	}

	// 计算情绪得分 (-1 到 1)
	totalWords := positiveCount + negativeCount
	if totalWords == 0 {
		return 0.0 // 中性
	}

	sentiment := float64(positiveCount-negativeCount) / float64(totalWords)

	// 限制在 -1 到 1 之间
	if sentiment > 1.0 {
		sentiment = 1.0
	} else if sentiment < -1.0 {
		sentiment = -1.0
	}

	return sentiment
}

// 获取情绪分析结果
func (s *SentimentService) GetSentimentAnalysis(symbol string, hours int) (*SentimentAnalysis, error) {
	hoursAgo := time.Now().Add(-time.Duration(hours) * time.Hour).Unix()

	var sentiments []config.SentimentData
	err := s.db.Where("symbol = ? AND timestamp > ?", symbol, hoursAgo).
		Find(&sentiments).Error

	if err != nil {
		return nil, err
	}

	if len(sentiments) == 0 {
		return &SentimentAnalysis{
			Symbol:           symbol,
			OverallSentiment: 0.0,
			NewsCount:        0,
			SocialCount:      0,
			Timestamp:        time.Now().Unix(),
		}, nil
	}

	// 计算总体情绪
	totalSentiment := 0.0
	newsCount := 0
	socialCount := 0

	for _, sentiment := range sentiments {
		totalSentiment += sentiment.Sentiment
		if sentiment.Source == "news" {
			newsCount++
		} else if sentiment.Source == "social" {
			socialCount++
		}
	}

	overallSentiment := totalSentiment / float64(len(sentiments))

	// 计算情绪趋势
	trend := s.calculateSentimentTrend(sentiments)

	analysis := &SentimentAnalysis{
		Symbol:           symbol,
		OverallSentiment: overallSentiment,
		NewsCount:        newsCount,
		SocialCount:      socialCount,
		Trend:            trend,
		Timestamp:        time.Now().Unix(),
	}

	return analysis, nil
}

// 计算情绪趋势
func (s *SentimentService) calculateSentimentTrend(sentiments []config.SentimentData) string {
	if len(sentiments) < 2 {
		return "NEUTRAL"
	}

	// 将数据分为两半，比较前半部分和后半部分的平均情绪
	mid := len(sentiments) / 2

	firstHalf := sentiments[:mid]
	secondHalf := sentiments[mid:]

	firstAvg := 0.0
	for _, s := range firstHalf {
		firstAvg += s.Sentiment
	}
	firstAvg /= float64(len(firstHalf))

	secondAvg := 0.0
	for _, s := range secondHalf {
		secondAvg += s.Sentiment
	}
	secondAvg /= float64(len(secondHalf))

	diff := secondAvg - firstAvg

	if diff > 0.1 {
		return "IMPROVING"
	} else if diff < -0.1 {
		return "DECLINING"
	}

	return "STABLE"
}

// 情绪分析结果结构
type SentimentAnalysis struct {
	Symbol           string  `json:"symbol"`
	OverallSentiment float64 `json:"overall_sentiment"`
	NewsCount        int     `json:"news_count"`
	SocialCount      int     `json:"social_count"`
	Trend            string  `json:"trend"`
	Timestamp        int64   `json:"timestamp"`
}

// 获取情绪历史数据
func (s *SentimentService) GetSentimentHistory(symbol string, hours int) ([]SentimentPoint, error) {
	hoursAgo := time.Now().Add(-time.Duration(hours) * time.Hour).Unix()

	var sentiments []config.SentimentData
	err := s.db.Where("symbol = ? AND timestamp > ?", symbol, hoursAgo).
		Order("timestamp ASC").
		Find(&sentiments).Error

	if err != nil {
		return nil, err
	}

	// 按小时聚合数据
	hourlyData := make(map[int64][]float64)

	for _, sentiment := range sentiments {
		hour := sentiment.Timestamp / 3600 * 3600 // 向下取整到小时
		hourlyData[hour] = append(hourlyData[hour], sentiment.Sentiment)
	}

	var points []SentimentPoint
	for timestamp, values := range hourlyData {
		avg := 0.0
		for _, value := range values {
			avg += value
		}
		avg /= float64(len(values))

		points = append(points, SentimentPoint{
			Timestamp: timestamp,
			Sentiment: avg,
			Count:     len(values),
		})
	}

	return points, nil
}

type SentimentPoint struct {
	Timestamp int64   `json:"timestamp"`
	Sentiment float64 `json:"sentiment"`
	Count     int     `json:"count"`
}
