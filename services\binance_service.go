package services

import (
	"context"
	"fmt"
	"log"
	"math/rand/v2"
	"strconv"
	"time"

	"kownbit/config"

	"github.com/adshao/go-binance/v2"
	"gorm.io/gorm"
)

type BinanceService struct {
	client           *binance.Client
	db               *gorm.DB
	demoMode         bool
	coinGeckoService *CoinGeckoService
}

func NewBinanceService(apiKey, secretKey string) *BinanceService {
	var client *binance.Client
	demoMode := apiKey == "demo_api_key" || apiKey == ""

	if !demoMode {
		client = binance.NewClient(apiKey, secretKey)
	}

	return &BinanceService{
		client:   client,
		demoMode: demoMode,
	}
}

func (s *BinanceService) SetDB(db *gorm.DB) {
	s.db = db
}

func (s *BinanceService) SetCoinGeckoService(cgs *CoinGeckoService) {
	s.coinGeckoService = cgs
}

// 启动数据采集
func (s *BinanceService) StartDataCollection() {
	symbols := []string{"BTCUSDT", "ETHUSDT", "BNBUSDT"}
	intervals := []string{"1m"} // 只采集1分钟数据，减少启动负载

	for _, symbol := range symbols {
		for _, interval := range intervals {
			go s.collectKlineData(symbol, interval)
		}
	}
}

// 采集K线数据
func (s *BinanceService) collectKlineData(symbol, interval string) {
	// 根据时间间隔设置不同的采集频率
	var duration time.Duration
	switch interval {
	case "1m":
		duration = 1 * time.Minute
	case "5m":
		duration = 5 * time.Minute
	case "15m":
		duration = 15 * time.Minute
	case "1h":
		duration = 1 * time.Hour
	default:
		duration = 1 * time.Minute
	}

	ticker := time.NewTicker(duration)
	defer ticker.Stop()

	// 立即执行一次
	s.fetchAndSaveKlineData(symbol, interval)

	for {
		select {
		case <-ticker.C:
			s.fetchAndSaveKlineData(symbol, interval)
		}
	}
}

func (s *BinanceService) fetchAndSaveKlineData(symbol, interval string) {
	if s.demoMode {
		s.generateDemoKlineData(symbol, interval)
		return
	}

	klines, err := s.client.NewKlinesService().
		Symbol(symbol).
		Interval(interval).
		Limit(100).
		Do(context.Background())

	if err != nil {
		log.Printf("Error fetching kline data for %s %s: %v", symbol, interval, err)
		s.generateDemoKlineData(symbol, interval)
		return
	}

	for _, kline := range klines {
		openPrice, _ := strconv.ParseFloat(kline.Open, 64)
		highPrice, _ := strconv.ParseFloat(kline.High, 64)
		lowPrice, _ := strconv.ParseFloat(kline.Low, 64)
		closePrice, _ := strconv.ParseFloat(kline.Close, 64)
		volume, _ := strconv.ParseFloat(kline.Volume, 64)
		quoteVolume, _ := strconv.ParseFloat(kline.QuoteAssetVolume, 64)

		klineData := &config.KlineData{
			Symbol:      symbol,
			OpenTime:    kline.OpenTime,
			CloseTime:   kline.CloseTime,
			Open:        openPrice,
			High:        highPrice,
			Low:         lowPrice,
			Close:       closePrice,
			Volume:      volume,
			QuoteVolume: quoteVolume,
			Interval:    interval,
		}

		// 使用UPSERT避免重复数据
		s.db.Where("symbol = ? AND open_time = ? AND interval = ?",
			symbol, kline.OpenTime, interval).FirstOrCreate(klineData)
	}
}

// 获取当前价格
func (s *BinanceService) GetCurrentPrice(symbol string) (float64, error) {
	// 优先使用CoinGecko真实数据
	if s.coinGeckoService != nil {
		if price, err := s.coinGeckoService.GetRealTimePrice(symbol); err == nil {
			return price, nil
		}
	}

	if s.demoMode {
		return s.getDemoPrice(symbol), nil
	}

	ticker, err := s.client.NewListPriceChangeStatsService().
		Symbol(symbol).
		Do(context.Background())

	if err != nil {
		log.Printf("获取价格失败，使用演示数据: %v", err)
		return s.getDemoPrice(symbol), nil
	}

	if len(ticker) > 0 {
		price, err := strconv.ParseFloat(ticker[0].LastPrice, 64)
		if err != nil {
			return s.getDemoPrice(symbol), nil
		}
		return price, nil
	}

	return s.getDemoPrice(symbol), nil
}

// 生成演示价格数据
func (s *BinanceService) getDemoPrice(symbol string) float64 {
	basePrice := map[string]float64{
		"BTCUSDT": 45000.0,
		"ETHUSDT": 3000.0,
		"BNBUSDT": 400.0,
	}

	base, exists := basePrice[symbol]
	if !exists {
		base = 1000.0
	}

	// 添加随机波动 (-2% 到 +2%)
	variation := (rand.Float64() - 0.5) * 0.04
	return base * (1 + variation)
}

// 获取深度数据
func (s *BinanceService) GetOrderBook(symbol string) (*binance.DepthResponse, error) {
	if s.demoMode {
		return nil, fmt.Errorf("演示模式不支持深度数据")
	}

	return s.client.NewDepthService().
		Symbol(symbol).
		Limit(100).
		Do(context.Background())
}

// 获取24小时统计数据
func (s *BinanceService) Get24hrStats(symbol string) (*binance.PriceChangeStats, error) {
	if s.demoMode {
		return s.getDemoStats(symbol), nil
	}

	stats, err := s.client.NewListPriceChangeStatsService().
		Symbol(symbol).
		Do(context.Background())

	if err != nil {
		log.Printf("获取24小时统计失败，使用演示数据: %v", err)
		return s.getDemoStats(symbol), nil
	}

	if len(stats) > 0 {
		return stats[0], nil
	}

	return s.getDemoStats(symbol), nil
}

// 生成演示统计数据
func (s *BinanceService) getDemoStats(symbol string) *binance.PriceChangeStats {
	currentPrice := s.getDemoPrice(symbol)
	change := (rand.Float64() - 0.5) * 0.1 // -5% 到 +5%

	return &binance.PriceChangeStats{
		Symbol:             symbol,
		LastPrice:          fmt.Sprintf("%.2f", currentPrice),
		PriceChange:        fmt.Sprintf("%.2f", currentPrice*change),
		PriceChangePercent: fmt.Sprintf("%.2f", change*100),
		Volume:             fmt.Sprintf("%.2f", rand.Float64()*1000000),
		QuoteVolume:        fmt.Sprintf("%.2f", rand.Float64()*1000000000),
		Count:              int64(rand.Float64() * 100000),
	}
}

// 获取历史K线数据用于分析
func (s *BinanceService) GetHistoricalKlines(symbol, interval string, limit int) ([]*config.KlineData, error) {
	var klines []*config.KlineData

	err := s.db.Where("symbol = ? AND interval = ?", symbol, interval).
		Order("open_time DESC").
		Limit(limit).
		Find(&klines).Error

	return klines, err
}

// 生成演示K线数据
func (s *BinanceService) generateDemoKlineData(symbol, interval string) {
	if s.db == nil {
		return
	}

	basePrice := s.getDemoPrice(symbol)
	now := time.Now()

	// 检查是否已有数据
	var count int64
	s.db.Model(&config.KlineData{}).
		Where("symbol = ? AND interval = ?", symbol, interval).
		Count(&count)

	if count >= 10 {
		return // 已有足够数据
	}

	// 生成最近10个K线数据
	for i := 9; i >= 0; i-- {
		openTime := now.Add(-time.Duration(i)*time.Minute).Unix() * 1000
		closeTime := openTime + 60000 // 1分钟后

		// 生成OHLC数据
		open := basePrice * (0.98 + rand.Float64()*0.04)
		high := open * (1 + rand.Float64()*0.02)
		low := open * (1 - rand.Float64()*0.02)
		close := low + rand.Float64()*(high-low)
		volume := rand.Float64() * 1000
		quoteVolume := volume * close

		klineData := &config.KlineData{
			Symbol:      symbol,
			OpenTime:    openTime,
			CloseTime:   closeTime,
			Open:        open,
			High:        high,
			Low:         low,
			Close:       close,
			Volume:      volume,
			QuoteVolume: quoteVolume,
			Interval:    interval,
		}

		// 使用UPSERT避免重复数据
		s.db.Where("symbol = ? AND open_time = ? AND interval = ?",
			symbol, openTime, interval).FirstOrCreate(klineData)
	}
}
