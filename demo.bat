@echo off
echo ========================================
echo 🚀 KownBit 币安期货预判系统 - 演示模式
echo ========================================
echo.

echo 📝 检查配置文件...
if not exist .env (
    echo DATABASE_URL=sqlite://kownbit.db > .env
    echo REDIS_URL=redis://localhost:6379 >> .env
    echo BINANCE_API_KEY=demo_api_key >> .env
    echo BINANCE_SECRET_KEY=demo_secret_key >> .env
    echo NEWS_API_KEY=demo_news_key >> .env
    echo PORT=8080 >> .env
    echo LOG_LEVEL=info >> .env
    echo ENVIRONMENT=demo >> .env
    echo DEMO_MODE=true >> .env
    echo ✅ 演示配置文件已创建
) else (
    echo ✅ 配置文件已存在
)

echo.
echo 🔧 编译应用程序...
go build -o kownbit.exe .
if %errorlevel% neq 0 (
    echo ❌ 编译失败，请检查Go环境
    echo 确保已安装Go 1.21+
    pause
    exit /b 1
)
echo ✅ 编译成功

echo.
echo 🎯 启动演示服务器...
echo 📱 服务器启动后，请在浏览器中访问: http://localhost:8080
echo 🛑 按 Ctrl+C 停止服务器
echo.
echo 正在启动...

start "" "http://localhost:8080"
kownbit.exe
